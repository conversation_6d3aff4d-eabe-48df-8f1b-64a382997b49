# ZBRS v1.0 Hierarchical Repository Implementation Plan

## Overview

This document outlines the comprehensive implementation plan for updating the Zaphnath codebase to support the new ZBRS v1.0 hierarchical repository structure. The changes implement a breaking change from single-repository structure to parent repository with translation subdirectories.

**IMPORTANT**: This implementation standardizes on `manifest.json` for all file types to eliminate confusion and URL transformation hacks.

## File Naming Standard

### Current Problematic Structure:
```
zbrs-registry/index.json          # Registry of collections
zbrs-official/index.json          # Discovery file 
zbrs-official/manifest.json       # Repository manifest (causes confusion)
kjv-1769/manifest.json           # Translation manifest
```

### New Standardized Structure:
```
zbrs-registry/manifest.json       # Registry manifest (lists collections)
zbrs-official/manifest.json       # Repository manifest (with translations array)
kjv-1769/manifest.json           # Translation manifest
```

**Benefits**:
- Eliminates URL transformation from `index.json` → `manifest.json`
- Clear, consistent naming across all levels
- Removes confusion about file purposes
- Simplifies discovery and import logic

## Scope of Changes

### Core Changes Required
- **File Naming**: Standardize all discovery/manifest files to `manifest.json`
- **Repository Discovery**: Update to expect `manifest.json` URLs directly
- **Registry Structure**: Update zbrs-registry to point to `manifest.json` files
- **URL Transformation**: Remove all `index.json` → `manifest.json` hacks
- **Repository Discovery**: Update to handle parent repositories with `translations` array
- **Manifest Processing**: Support both parent and translation manifests
- **Import Logic**: Handle hierarchical imports (full repository vs individual translations)
- **Database Schema**: Add support for translation organization
- **UI Components**: Update to display hierarchical structure
- **Type Definitions**: Add new types for parent repositories and translations

## Implementation Order & Dependencies

### Phase 1: Core Type System Updates
**Priority**: Critical - Foundation for all other changes
**Dependencies**: None

#### 1.1 Update Type Definitions
**File**: `packages/main/src/services/repository/types.ts`
**Changes Required**:
```typescript
// Add new interfaces for hierarchical structure
export interface ZBRSParentManifest {
  zbrs_version: string;
  repository: ParentRepositoryInfo;
  publisher: PublisherInfo;
  translations: TranslationReference[];
  technical: TechnicalInfo;
  extensions?: Record<string, ExtensionInfo>;
}

export interface ParentRepositoryInfo {
  id: string;
  name: string;
  description: string;
  version: string;
  type: "parent";
  created_at: string;
  updated_at: string;
}

export interface TranslationReference {
  id: string;
  name: string;
  directory: string;
  language: LanguageInfo;
  status: "active" | "inactive" | "deprecated";
}

export interface ZBRSTranslationManifest {
  zbrs_version: string;
  repository: RepositoryInfo; // Existing interface, no type field
  content: ContentInfo;
  technical: TechnicalInfo;
  extensions?: Record<string, ExtensionInfo>;
}

// Update existing ZBRSManifest to be union type
export type ZBRSManifest = ZBRSParentManifest | ZBRSTranslationManifest;
```

#### 1.2 Update Frontend Types
**File**: `packages/renderer/src/types/store.ts`
**Changes Required**:
```typescript
export interface Repository {
  id: string;
  name: string;
  description: string;
  language?: string; // Optional for parent repositories
  version: string;
  created_at: string;
  updated_at: string;
  type?: "parent" | "translation"; // Add type field
  parent_id?: string; // For translation repositories
  book_count?: number;
  verse_count?: number;
  is_active?: boolean;
  translations?: TranslationInfo[]; // For parent repositories
}

export interface TranslationInfo {
  id: string;
  name: string;
  directory: string;
  language: string;
  status: string;
  book_count?: number;
  verse_count?: number;
}
```

### Phase 2: Database Schema Updates
**Priority**: Critical - Required before any data operations
**Dependencies**: Phase 1 complete

#### 2.1 Add New Migration
**File**: `packages/main/src/services/database/migrations.ts`
**Changes Required**:
```sql
-- Migration version 5: Add hierarchical repository support
CREATE TABLE IF NOT EXISTS repository_translations (
  id TEXT PRIMARY KEY,
  parent_repository_id TEXT NOT NULL,
  translation_id TEXT NOT NULL,
  directory_name TEXT NOT NULL,
  language_code TEXT NOT NULL,
  status TEXT DEFAULT 'active',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (parent_repository_id) REFERENCES repositories(id) ON DELETE CASCADE,
  FOREIGN KEY (translation_id) REFERENCES repositories(id) ON DELETE CASCADE
);

-- Add type column to repositories table
ALTER TABLE repositories ADD COLUMN type TEXT DEFAULT 'translation';
ALTER TABLE repositories ADD COLUMN parent_id TEXT;

-- Add foreign key for parent relationship
CREATE INDEX IF NOT EXISTS idx_repositories_parent ON repositories(parent_id);
CREATE INDEX IF NOT EXISTS idx_repository_translations_parent ON repository_translations(parent_repository_id);
```

#### 2.2 Update Database Queries
**File**: `packages/main/src/services/database/queries.ts`
**Changes Required**:
- Add methods for parent repository operations
- Add methods for translation relationship management
- Update existing queries to handle hierarchical structure

### Phase 3: Repository Discovery & Validation
**Priority**: High - Core functionality
**Dependencies**: Phase 1, 2 complete

#### 3.1 Update Repository Discovery Service
**File**: `packages/main/src/services/repository/discovery.ts`
**Changes Required**:
- **REMOVE**: All `index.json` handling and URL transformation logic
- Update `fetchRepositoryManifest()` to work exclusively with `manifest.json`
- Add `fetchTranslationManifest()` method for individual translations
- Update `scanDirectoryForRepositories()` to handle hierarchical structure
- Add `discoverTranslations()` method to parse parent manifest translations array

#### 3.2 Update Frontend Discovery Component
**File**: `packages/renderer/src/components/repository/RepositoryDiscovery.tsx`
**Changes Required**:
- **REMOVE**: URL transformation from `index.json` to `manifest.json`
- **REMOVE**: Logic for handling different file types (`endsWith('.json')` checks)
- Expect registry to provide direct `manifest.json` URLs
- Simplify repository action handling (no more file type detection)

### Phase 4: Import System Overhaul
**Priority**: High - Core functionality
**Dependencies**: Phase 1, 2, 3 complete

#### 4.1 Update Repository Importer
**File**: `packages/main/src/services/repository/importer.ts`
**Changes Required**:
- Add `importParentRepository()` method
- Add `importTranslation()` method
- Update `importRepository()` to detect and route appropriately
- Add logic to handle translation directory scanning
- Update book import to use translation-specific paths

#### 4.2 Update Repository Service
**File**: `packages/main/src/services/repository/index.ts`
**Changes Required**:
- Add methods for translation-specific operations
- Update existing methods to handle hierarchical structure
- Add `getTranslations()` method for parent repositories

### Phase 5: UI Component Updates
**Priority**: Medium - User interface
**Dependencies**: Phase 1, 2, 3, 4 complete

#### 5.1 Update Repository Import Dialog
**File**: `packages/renderer/src/components/repository/RepositoryImportDialog.tsx`
**Changes Required**:
- Add UI for selecting import type (full repository vs translation)
- Update validation display for hierarchical structure
- Add translation selection interface for parent repositories
- Update progress reporting for hierarchical imports

#### 5.2 Update Repository List Component
**File**: `packages/renderer/src/components/repository/RepositoryList.tsx`
**Changes Required**:
- Add hierarchical display (parent repositories with expandable translations)
- Update repository cards to show type and translation count
- Add translation-specific actions and information

#### 5.3 Update Repository Management
**File**: `packages/renderer/src/components/repository/RepositoryManagement.tsx`
**Changes Required**:
- Update statistics to handle hierarchical structure
- Add parent repository management features
- Update quick actions for translation management

#### 5.4 Update Repository Discovery
**File**: `packages/renderer/src/components/repository/RepositoryDiscovery.tsx`
**Changes Required**:
- Update to display parent repositories with translation info
- Add filtering by translation language
- Update repository selection to handle hierarchical structure

### Phase 6: Store and State Management
**Priority**: Medium - Data management
**Dependencies**: Phase 1, 5 complete

#### 6.1 Update Repository Store
**File**: `packages/renderer/src/stores/repositoryStore.ts`
**Changes Required**:
- Add state for parent repositories and translations
- Update actions to handle hierarchical operations
- Add translation selection and management actions
- Update loading and error handling for hierarchical structure

## Migration Strategy

### Repository Structure Migration
1. **zbrs-registry Updates**
   - Rename `index.json` → `manifest.json`
   - Update all collection URLs to point to `manifest.json` files
   - Update manifest schema to be registry-specific

2. **zbrs-official Updates**
   - **REMOVE**: `index.json` file entirely
   - Keep only `manifest.json` with repository structure
   - Ensure `translations` array contains all translations

3. **Translation Repositories**
   - All individual translations keep `manifest.json` naming
   - No changes needed to translation structure

### Code Migration
1. **Remove URL Transformation Logic**
   - Clean up `RepositoryDiscovery` component
   - Remove `index.json` → `manifest.json` replacement code
   - Update registry handling to expect direct URLs

2. **Update Registry Expectations**
   - Registry entries should have `manifest.json` URLs
   - Remove file type detection and branching logic
   - Simplify import flow

## Testing Strategy

### Unit Tests
1. **Type System Tests**
   - Validate new type definitions
   - Test type guards for parent vs translation manifests

2. **Database Tests**
   - Test migration execution
   - Validate hierarchical queries
   - Test foreign key constraints

3. **Discovery Service Tests**
   - Test parent manifest parsing
   - Test translation discovery
   - Test directory scanning with hierarchical structure

4. **Import Service Tests**
   - Test parent repository import
   - Test individual translation import
   - Test error handling for invalid structures

5. **Validation Tests**
   - Test parent manifest validation
   - Test translation manifest validation
   - Test schema compliance

### Integration Tests
1. **End-to-End Import Tests**
   - Test complete repository import flow
   - Test translation-only import flow
   - Test error scenarios and rollback

2. **UI Integration Tests**
   - Test repository selection and import UI
   - Test hierarchical display and navigation
   - Test translation management operations

### Test Data Requirements
1. **Example Repositories**
   - Create test parent repositories with multiple translations
   - Create individual translation directories
   - Create invalid structures for error testing

2. **Mock Services**
   - Mock network requests for repository discovery
   - Mock file system operations for local imports
   - Mock database operations for unit tests

## Risk Assessment

### High Risk Areas
1. **Data Loss**: Database migration could fail
   - **Mitigation**: Comprehensive backup and rollback procedures

2. **Import Failures**: Complex hierarchical import logic
   - **Mitigation**: Extensive testing with various repository structures

3. **UI Complexity**: Hierarchical display could confuse users
   - **Mitigation**: Clear UI design and user testing

### Medium Risk Areas
1. **Performance**: Additional database queries for hierarchical structure
   - **Mitigation**: Optimize queries and add appropriate indexes

2. **Validation Complexity**: Multiple manifest types to validate
   - **Mitigation**: Clear separation of validation logic

## Success Criteria

1. **Functional Requirements**
   - Support parent repositories with multiple translations
   - Support individual translation imports
   - Maintain data integrity in hierarchical structure
   - Provide clear UI for hierarchical navigation

2. **Performance Requirements**
   - Repository discovery time < 5 seconds
   - Import time comparable to current implementation
   - UI responsiveness maintained

3. **Quality Requirements**
   - 100% test coverage for new functionality
   - No data loss during migration
   - Clear error messages for invalid structures

## Timeline Estimate

- **Phase 1**: 2-3 days
- **Phase 2**: 2-3 days  
- **Phase 3**: 3-4 days
- **Phase 4**: 4-5 days
- **Phase 5**: 5-6 days
- **Phase 6**: 2-3 days
- **Testing & Integration**: 3-4 days

**Total Estimated Time**: 21-28 days

## Next Steps

1. Begin with Phase 1 (Type System Updates)
2. Create comprehensive test suite alongside implementation
3. Regular testing with example hierarchical repositories
4. User testing of UI changes before final release
5. Documentation updates throughout implementation

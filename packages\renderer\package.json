{"name": "@app/renderer", "private": true, "version": "0.0.0", "type": "module", "author": {"email": "<EMAIL>", "name": "Beabfekad Zikie", "url": "https://beabzk.github.io"}, "scripts": {"dev": "vite", "build": "tsc && vite build --base ./", "preview": "vite preview", "typecheck": "tsc --noEmit"}, "dependencies": {"@radix-ui/react-separator": "^1.1.7", "@tailwindcss/postcss": "^4.1.11", "autoprefixer": "^10.4.21", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.536.0", "postcss": "^8.5.6", "react": "^19.1.1", "react-dom": "^19.1.1", "react-intersection-observer": "^9.16.0", "react-window": "^1.8.11", "react-window-infinite-loader": "^1.0.10", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.11", "zustand": "^5.0.7"}, "devDependencies": {"@types/node": "^24.2.0", "@types/react": "^19.1.9", "@types/react-dom": "^19.1.7", "@vitejs/plugin-react": "^4.7.0", "typescript": "^5.9.2", "vite": "^7.0.6"}, "main": "./dist/index.html", "exports": {".": {"default": "./dist/index.html"}}}
{"zbrs_version": "1.0", "repository": {"id": "test-hierarchical-parent", "name": "Test Hierarchical Parent", "description": "A test repository for hierarchical imports.", "version": "1.0.0", "type": "parent", "created_at": "2024-01-01T00:00:00Z", "updated_at": "2024-01-01T00:00:00Z"}, "publisher": {"name": "<PERSON><PERSON>nath Test Suite"}, "translations": [{"id": "test-translation-1", "name": "Test Translation", "directory": "test-translation", "language": {"code": "eng", "name": "English", "direction": "ltr"}, "status": "active"}], "technical": {"encoding": "UTF-8", "compression": "none", "checksum": "placeholder-checksum", "size_bytes": 0}}
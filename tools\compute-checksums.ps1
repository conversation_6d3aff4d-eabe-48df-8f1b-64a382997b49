param(
  [string]$Root = (Split-Path -Parent $PSScriptRoot),
  [string]$TranslationId,
  [switch]$All,
  [switch]$Repo,
  [switch]$Update
)

Set-StrictMode -Version Latest
$ErrorActionPreference = 'Stop'

function Get-TranslationDirs([string]$root) {
  Get-ChildItem -Path $root -Directory | Where-Object { Test-Path (Join-Path $_.FullName 'manifest.json') }
}

function Get-TranslationId([string]$dir) {
  $manPath = Join-Path $dir 'manifest.json'
  if (Test-Path $manPath) {
    try {
      $j = Get-Content -LiteralPath $manPath -Raw | ConvertFrom-Json
      if ($j.repository -and $j.repository.id) { return [string]$j.repository.id }
    } catch {}
  }
  return (Split-Path -Leaf $dir)
}

function Get-IncludedFileItems([string]$baseDir, [string]$relBase, [string[]]$excludeNames) {
  $files = Get-ChildItem -Path $baseDir -File -Recurse |
    Where-Object { $excludeNames -notcontains $_.Name }
  foreach ($f in $files) {
    $rel = $f.FullName.Substring((Resolve-Path $relBase).Path.Length) -replace '^[\\/]+'
    $rel = ($rel -replace '\\','/')
    # Normalize to lower for cross-platform determinism
    $relLower = $rel.ToLowerInvariant()
    [PSCustomObject]@{ File = $f; Rel = $relLower }
  }
}

function Compute-HashFromItems($items) {
  $enc = New-Object System.Text.UTF8Encoding($false)
  $nullStream = [System.IO.Stream]::Null
  $sha = [System.Security.Cryptography.SHA256]::Create()
  $cs = New-Object System.Security.Cryptography.CryptoStream($nullStream,$sha,[System.Security.Cryptography.CryptoStreamMode]::Write)
  $size = 0
  foreach ($it in ($items | Sort-Object Rel)) {
    $nameBytes = $enc.GetBytes($it.Rel + "`n")
    $cs.Write($nameBytes,0,$nameBytes.Length)
    $fs = [System.IO.File]::OpenRead($it.File.FullName)
    $fs.CopyTo($cs)
    $size += $it.File.Length
    $fs.Dispose()
    $cs.WriteByte(10)
  }
  $cs.FlushFinalBlock()
  $hashHex = -join ($sha.Hash | ForEach-Object { $_.ToString('x2') })
  [PSCustomObject]@{ Hash = $hashHex; Size = [int64]$size }
}

function Update-TranslationManifest([string]$trDir, [string]$checksumHex, [int64]$sizeBytes) {
  $manPath = Join-Path $trDir 'manifest.json'
  if (-not (Test-Path $manPath)) { return }
  $json = Get-Content -LiteralPath $manPath -Raw | ConvertFrom-Json
  if (-not $json.technical) { $json | Add-Member -NotePropertyName technical -NotePropertyValue (@{}) }
  if (-not ($json.technical.PSObject.Properties.Name -contains 'encoding') -or [string]::IsNullOrWhiteSpace([string]$json.technical.encoding)) {
    if (-not ($json.technical.PSObject.Properties.Name -contains 'encoding')) {
      $json.technical | Add-Member -NotePropertyName encoding -NotePropertyValue 'UTF-8'
    } else { $json.technical.encoding = 'UTF-8' }
  }
  if (-not ($json.technical.PSObject.Properties.Name -contains 'compression') -or [string]::IsNullOrWhiteSpace([string]$json.technical.compression)) {
    if (-not ($json.technical.PSObject.Properties.Name -contains 'compression')) {
      $json.technical | Add-Member -NotePropertyName compression -NotePropertyValue 'none'
    } else { $json.technical.compression = 'none' }
  }
  $json.technical.checksum = "sha256:$checksumHex"
  $json.technical.size_bytes = [int64]$sizeBytes
  if ($json.repository) { $json.repository.updated_at = [DateTime]::UtcNow.ToString('o') }
  ($json | ConvertTo-Json -Depth 32) | Set-Content -LiteralPath $manPath -Encoding utf8
}

function Update-RepoManifest([string]$root, [string]$checksumHex, [int64]$sizeBytes) {
  $manPath = Join-Path $root 'manifest.json'
  if (-not (Test-Path $manPath)) { return }
  $json = Get-Content -LiteralPath $manPath -Raw | ConvertFrom-Json
  if (-not $json.technical) { $json | Add-Member -NotePropertyName technical -NotePropertyValue (@{}) }
  if (-not ($json.technical.PSObject.Properties.Name -contains 'encoding') -or [string]::IsNullOrWhiteSpace([string]$json.technical.encoding)) {
    if (-not ($json.technical.PSObject.Properties.Name -contains 'encoding')) {
      $json.technical | Add-Member -NotePropertyName encoding -NotePropertyValue 'UTF-8'
    } else { $json.technical.encoding = 'UTF-8' }
  }
  if (-not ($json.technical.PSObject.Properties.Name -contains 'compression') -or [string]::IsNullOrWhiteSpace([string]$json.technical.compression)) {
    if (-not ($json.technical.PSObject.Properties.Name -contains 'compression')) {
      $json.technical | Add-Member -NotePropertyName compression -NotePropertyValue 'none'
    } else { $json.technical.compression = 'none' }
  }
  $json.technical.checksum = "sha256:$checksumHex"
  $json.technical.size_bytes = [int64]$sizeBytes
  if ($json.repository) { $json.repository.updated_at = [DateTime]::UtcNow.ToString('o') }
  ($json | ConvertTo-Json -Depth 32) | Set-Content -LiteralPath $manPath -Encoding utf8
}

# Determine scope
$excludeNames = @('manifest.json','README.md')
$translations = @()
$repoSummary = $null

if ($TranslationId) {
  $trDir = Join-Path $Root $TranslationId
  if (-not (Test-Path $trDir)) { throw "Translation folder not found: $trDir" }
  $items = Get-IncludedFileItems -baseDir $trDir -relBase $trDir -excludeNames $excludeNames
  $result = Compute-HashFromItems -items $items
  $tid = Get-TranslationId -dir $trDir
  $trObj = [PSCustomObject]@{ id=$tid; path=(Split-Path -Leaf $trDir); checksum="sha256:$($result.Hash)"; size_bytes=$result.Size }
  $translations += $trObj
  if ($Update) { Update-TranslationManifest -trDir $trDir -checksumHex $result.Hash -sizeBytes $result.Size }
}

if ($All) {
  foreach ($d in (Get-TranslationDirs -root $Root)) {
    $items = Get-IncludedFileItems -baseDir $d.FullName -relBase $d.FullName -excludeNames $excludeNames
    $result = Compute-HashFromItems -items $items
    $tid = Get-TranslationId -dir $d.FullName
    $trObj = [PSCustomObject]@{ id=$tid; path=(Split-Path -Leaf $d.FullName); checksum="sha256:$($result.Hash)"; size_bytes=$result.Size }
    $translations += $trObj
    if ($Update) { Update-TranslationManifest -trDir $d.FullName -checksumHex $result.Hash -sizeBytes $result.Size }
  }
}

if ($Repo -or $All) {
  $allItems = @()
  foreach ($d in (Get-TranslationDirs -root $Root)) {
    $allItems += Get-IncludedFileItems -baseDir $d.FullName -relBase $Root -excludeNames $excludeNames
  }
  $repoRes = Compute-HashFromItems -items $allItems
  $repoSummary = [PSCustomObject]@{ checksum = "sha256:$($repoRes.Hash)"; size_bytes = $repoRes.Size }
  if ($Update) { Update-RepoManifest -root $Root -checksumHex $repoRes.Hash -sizeBytes $repoRes.Size }
}

# Default behavior if no scope specified: compute all translations and repo summary
if (-not $TranslationId -and -not $All -and -not $Repo) {
  $All = $true
  $Repo = $true
  foreach ($d in (Get-TranslationDirs -root $Root)) {
    $items = Get-IncludedFileItems -baseDir $d.FullName -relBase $d.FullName -excludeNames $excludeNames
    $result = Compute-HashFromItems -items $items
    $tid = Get-TranslationId -dir $d.FullName
    $translations += [PSCustomObject]@{ id=$tid; path=(Split-Path -Leaf $d.FullName); checksum="sha256:$($result.Hash)"; size_bytes=$result.Size }
  }
  $allItems = @()
  foreach ($d in (Get-TranslationDirs -root $Root)) {
    $allItems += Get-IncludedFileItems -baseDir $d.FullName -relBase $Root -excludeNames $excludeNames
  }
  $repoRes = Compute-HashFromItems -items $allItems
  $repoSummary = [PSCustomObject]@{ checksum = "sha256:$($repoRes.Hash)"; size_bytes = $repoRes.Size }
}

$out = [ordered]@{ zbrs_version = '1.0'; root = $Root }
if ($translations.Count -gt 0) { $out.translations = $translations }
if ($repoSummary) { $out.repo = $repoSummary }
$out | ConvertTo-Json -Depth 32

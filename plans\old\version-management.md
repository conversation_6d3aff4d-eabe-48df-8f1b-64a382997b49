# Version Management Guide

This document explains how to manage version numbers for the Zaphnath Bible Reader application following semantic versioning (semver) principles.

## Overview

Zaphnath Bible Reader uses **Semantic Versioning (semver)** with the format `MAJOR.MINOR.PATCH` (e.g., `0.1.0`, `1.2.3`, `2.0.0-beta.1`).

### Current Version System

- **Single Source of Truth**: Version is defined in the root `package.json` file
- **Automatic Propagation**: Version is automatically injected into the UI and build artifacts
- **Centralized Management**: All version-related operations use npm scripts

## Semantic Versioning for Bible Reader

### Version Number Format: `MAJOR.MINOR.PATCH[-PRERELEASE]`

#### MAJOR Version (X.0.0)
Increment when making **incompatible changes** that break existing functionality:

**Examples for Bible Reader:**
- Complete UI redesign that changes user workflows
- Database schema changes requiring migration
- Removal of major features (repository formats, search types)
- Breaking changes to repository import/export formats
- Changes to core Bible reading interface that require user relearning

**When to increment:**
- Users need to learn new ways to accomplish existing tasks
- Existing repositories might not work without conversion
- Settings or preferences are incompatible with previous versions

#### MINOR Version (0.X.0)
Increment when adding **new functionality** in a backward-compatible manner:

**Examples for Bible Reader:**
- New Bible repository formats support
- Additional search features (advanced filters, cross-references)
- New reading modes (study mode, comparison view)
- Audio features (text-to-speech, audio Bible support)
- New import/export capabilities
- Additional language support for the interface
- New themes or customization options
- Reading plans and study tools

**When to increment:**
- Adding features that enhance the app without breaking existing workflows
- New optional settings or preferences
- Performance improvements that users will notice
- New supported Bible formats or translation sources

#### PATCH Version (0.0.X)
Increment when making **backward-compatible bug fixes**:

**Examples for Bible Reader:**
- Fixing search result accuracy
- Correcting verse display formatting
- Resolving repository import errors
- UI bug fixes (layout, styling, responsiveness)
- Performance optimizations
- Security updates
- Dependency updates
- Documentation corrections

**When to increment:**
- Fixing issues without changing functionality
- Improving existing features without adding new ones
- Security patches
- Minor UI improvements

#### PRERELEASE Versions (0.1.0-alpha.1, 1.0.0-beta.2)
Use for testing versions before official release:

**Prerelease Types:**
- **alpha**: Early development, major features incomplete
- **beta**: Feature-complete, testing for bugs
- **rc** (release candidate): Final testing before release

## Version Management Commands

### Viewing Current Version

```bash
# Show current version
npm run version:show

# Check version in package.json
cat package.json | grep version
```

### Updating Versions

```bash
# Patch version (0.1.0 → 0.1.1)
npm run version:patch

# Minor version (0.1.0 → 0.2.0)
npm run version:minor

# Major version (0.1.0 → 1.0.0)
npm run version:major

# Prerelease version (0.1.0 → 0.1.1-0)
npm run version:prerelease
```

### Manual Version Setting

```bash
# Set specific version
npm version 1.2.3 --no-git-tag-version

# Set prerelease version
npm version 1.0.0-beta.1 --no-git-tag-version
```

## Automated Version Propagation

The version management system automatically propagates the version from `package.json` to:

### 1. Application UI
- **Header Component**: Displays version badge (e.g., "v0.1.0")
- **Debug Panel**: Shows detailed version information
- **About Dialog**: Version information for user reference

### 2. Build System
- **Vite Configuration**: Injects version as `__APP_VERSION__` global
- **Electron Builder**: Uses version for app metadata and file naming
- **Build Artifacts**: Version included in executable names

### 3. Development Tools
- **TypeScript**: Version available as typed global constant
- **Debug Tools**: Version included in debug reports and logs

## Technical Implementation

### Version Injection Process

1. **Build Time**: Vite reads version from root `package.json`
2. **Global Definition**: Version injected as `__APP_VERSION__` constant
3. **Type Safety**: TypeScript declaration provides type checking
4. **Utility Functions**: Helper functions in `@/lib/version.ts` for formatting

### File Structure

```
zaphnath/
├── package.json                           # Single source of truth
├── packages/renderer/
│   ├── vite.config.ts                    # Version injection
│   ├── src/vite-env.d.ts                 # TypeScript declarations
│   ├── src/lib/version.ts                # Version utilities
│   └── src/components/layout/Header.tsx  # UI display
└── plans/version-management.md           # This documentation
```

### Code Examples

```typescript
// Using version in components
import { getAppVersionWithPrefix, getVersionInfo } from '@/lib/version'

// Simple version display
const version = getAppVersionWithPrefix() // "v0.1.0"

// Detailed version information
const info = getVersionInfo()
console.log(info.version)        // "0.1.0"
console.log(info.major)          // 0
console.log(info.minor)          // 1
console.log(info.patch)          // 0
console.log(info.isPrerelease)   // false
```

## Release Process

### 1. Development Phase
- Work on features/fixes in development
- Use current version for testing

### 2. Pre-Release Testing
```bash
# Create beta version for testing
npm run version:prerelease
# Results in: 0.1.0 → 0.1.1-0

# Or create named prerelease
npm version 1.0.0-beta.1 --no-git-tag-version
```

### 3. Release Preparation
```bash
# For bug fixes
npm run version:patch

# For new features
npm run version:minor

# For breaking changes
npm run version:major
```

### 4. Build and Deploy
```bash
# Build application with new version
npm run build
npm run compile

# Version is automatically included in:
# - Application UI
# - Executable file names
# - Build metadata
```

### 5. Git Tagging (Manual)
```bash
# After version bump, create git tag
git add package.json
git commit -m "Bump version to $(npm run version:show --silent)"
git tag "v$(npm run version:show --silent)"
git push origin main --tags
```

## Best Practices

### Version Bumping Guidelines

1. **Always test** before bumping versions
2. **Update documentation** for minor/major releases
3. **Create release notes** for user-facing changes
4. **Use prerelease versions** for testing
5. **Follow semver strictly** to maintain user trust

### Release Timing

- **Patch releases**: As needed for critical fixes
- **Minor releases**: Monthly or when significant features are ready
- **Major releases**: Quarterly or when breaking changes are necessary

### Communication

- **Patch releases**: Brief changelog in commit message
- **Minor releases**: Detailed release notes with new features
- **Major releases**: Migration guide and comprehensive documentation

## Troubleshooting

### Version Not Updating in UI

1. **Clear build cache**: `rm -rf packages/renderer/dist`
2. **Rebuild application**: `npm run build`
3. **Check Vite config**: Ensure version injection is working
4. **Verify imports**: Check that components import version utilities correctly

### Build Issues After Version Change

1. **Clean install**: `rm -rf node_modules && npm install`
2. **Rebuild native modules**: `npm run rebuild`
3. **Check TypeScript**: `npm run typecheck`

### Git Tag Conflicts

1. **List existing tags**: `git tag -l`
2. **Delete conflicting tag**: `git tag -d v1.0.0`
3. **Push tag deletion**: `git push origin :refs/tags/v1.0.0`

## Future Enhancements

### Planned Improvements

1. **Automatic Changelog**: Generate changelogs from git commits
2. **Release Automation**: GitHub Actions for automated releases
3. **Version Validation**: Pre-commit hooks to validate version bumps
4. **Update Notifications**: In-app update notifications for users

### Integration Opportunities

1. **Electron Auto-Updater**: Integrate with version checking
2. **Analytics**: Track version adoption and usage
3. **Error Reporting**: Include version in crash reports
4. **Feature Flags**: Version-based feature rollouts

---

## Quick Reference

| Command | Purpose | Example |
|---------|---------|---------|
| `npm run version:show` | Display current version | `0.1.0` |
| `npm run version:patch` | Bug fix release | `0.1.0 → 0.1.1` |
| `npm run version:minor` | Feature release | `0.1.0 → 0.2.0` |
| `npm run version:major` | Breaking changes | `0.1.0 → 1.0.0` |
| `npm run version:prerelease` | Test version | `0.1.0 → 0.1.1-0` |

**Remember**: Version is the single source of truth in `package.json` and automatically propagates throughout the application.

{"name": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON> Bible Reader - A modern, secure Bible study application", "version": "0.1.0", "private": true, "type": "module", "author": {"email": "<EMAIL>", "name": "Beabfekad Zikie", "url": "https://beabzk.github.io"}, "repository": {"type": "git", "url": "https://github.com/beabzk/zaphnath.git"}, "homepage": "https://beabzk.github.io/zaphnath", "bugs": {"url": "https://github.com/beabzk/zaphnath/issues"}, "main": "packages/entry-point.mjs", "workspaces": ["packages/*"], "engines": {"node": ">=22.18.0"}, "scripts": {"build": "npm run build -ws --if-present", "compile": "npm run build && electron-builder build --config electron-builder.mjs", "start": "node packages/dev-mode.js", "typecheck": "npm run typecheck -ws --if-present", "create-renderer": "cd packages && npm create vite@latest renderer", "integrate-renderer": "npm start --workspace @app/integrate-renderer", "init": "npm run create-renderer && npm run integrate-renderer && npm install", "rebuild": "electron-rebuild", "postinstall": "electron-rebuild", "convert-bible-files": "bash tools/convert-bible-files.sh", "validate-zbrs": "node tools/validators/validate-zbrs.js", "test-import": "node tools/test-import.js", "version:patch": "npm version patch --no-git-tag-version", "version:minor": "npm version minor --no-git-tag-version", "version:major": "npm version major --no-git-tag-version", "version:prerelease": "npm version prerelease --no-git-tag-version", "version:show": "node -p \"require('./package.json').version\"", "version:info": "node tools/version-manager.js show", "version:set": "node tools/version-manager.js set", "version:help": "node tools/version-manager.js help", "lint": "eslint . --ext .ts,.tsx,.js,.jsx", "lint:fix": "eslint . --ext .ts,.tsx,.js,.jsx --fix"}, "devDependencies": {"@eslint/js": "^9.32.0", "@npmcli/map-workspaces": "4.0.2", "@types/better-sqlite3": "^7.6.13", "@types/node": "^24.2.0", "@typescript-eslint/eslint-plugin": "^8.39.0", "@typescript-eslint/parser": "^8.39.0", "electron": "^37.2.5", "electron-builder": "26.0.12", "electron-rebuild": "^3.2.9", "eslint": "^9.32.0", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "glob": "11.0.3"}, "dependencies": {"@app/main": "*", "@radix-ui/react-dropdown-menu": "^2.1.15"}}
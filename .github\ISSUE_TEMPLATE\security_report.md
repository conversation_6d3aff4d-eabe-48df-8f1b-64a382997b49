---
name: Security vulnerability report
about: Report a security vulnerability (please use private reporting for sensitive issues)
title: '[SECURITY] '
labels: security
assignees: beabzk

---

## ⚠️ Security Notice
For sensitive security issues, please use GitHub's private vulnerability reporting feature instead of creating a public issue.

## Vulnerability Description
A clear and concise description of the security vulnerability.

## Affected Components
- [ ] Main process (Electron)
- [ ] Renderer process (React UI)
- [ ] Preload scripts
- [ ] Database operations
- [ ] File system operations
- [ ] Network operations
- [ ] Dependencies

## Steps to Reproduce
1. Go to '...'
2. Click on '....'
3. Scroll down to '....'
4. See vulnerability

## Expected vs Actual Behavior
**Expected:** What should happen
**Actual:** What actually happens (security issue)

## Impact Assessment
- [ ] Low - Limited impact
- [ ] Medium - Moderate impact
- [ ] High - Significant impact
- [ ] Critical - Severe impact

## Environment
- OS: [e.g. Windows 11, macOS 14, Ubuntu 22.04]
- <PERSON><PERSON><PERSON> Version: [e.g. 1.0.0]
- Electron Version: [if known]

## Additional Context
Add any other context about the vulnerability here.

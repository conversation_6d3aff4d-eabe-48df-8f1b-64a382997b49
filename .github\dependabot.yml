version: 2
updates:
  # Maintain dependencies for GitHub Actions
  - package-ecosystem: "github-actions"
    directory: "/"
    schedule:
      interval: "weekly"
    assignees:
      - "beabzk"

  # Maintain dependencies for npm (root)
  - package-ecosystem: "npm"
    versioning-strategy: increase
    directory: "/"
    schedule:
      interval: "weekly"
    assignees:
      - "beabzk"
    groups:
      electron:
        patterns:
          - "electron*"
      playwright:
        patterns:
          - "@playwright/*"
          - "playwright"
      build-tools:
        patterns:
          - "vite*"
          - "@vitejs/*"
          - "typescript"
          - "electron-builder"

  # Maintain dependencies for main package
  - package-ecosystem: "npm"
    directory: "/packages/main"
    schedule:
      interval: "weekly"

  # Maintain dependencies for preload package
  - package-ecosystem: "npm"
    directory: "/packages/preload"
    schedule:
      interval: "weekly"

  # Maintain dependencies for renderer package
  - package-ecosystem: "npm"
    directory: "/packages/renderer"
    schedule:
      interval: "weekly"
    groups:
      react:
        patterns:
          - "react*"
          - "@types/react*"
      ui:
        patterns:
          - "@radix-ui/*"
          - "tailwind*"
          - "lucide-react"

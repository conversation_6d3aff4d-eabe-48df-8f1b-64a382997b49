name: Setup boilerplate
inputs:
  renderer-template:
    description: Define what vite template should be used to create renderer in case if renderer package doesn't exist
    required: false
    default: ''


runs:
  using: "composite"
  steps:
    - if: ${{ runner.os == 'Linux' }}
      shell: bash
      run: |
        Xvfb :99 -screen 0 1280x960x24 &
        echo "DISPLAY=:99" >> $GITHUB_ENV

    - uses: actions/setup-node@v4
      with:
        node-version: 'latest'

    - name: Check if renderer directory exists
      id: check-renderer
      run: >-
        if [ -d "packages/renderer" ]; then
          echo "RENDERER_EXIST=true" >> $GITHUB_OUTPUT
        else
          echo "RENDERER_EXIST=false" >> $GITHUB_OUTPUT
        fi
      shell: bash

    - run: |
        npm run create-renderer -- -- --template ${{inputs.renderer-template}}
        npm start --workspace @app/integrate-renderer
      shell: bash
      if: inputs.renderer-template != '' && steps.check-renderer.outputs.RENDERER_EXIST == 'false'

    - name: <PERSON><PERSON> Dependencies
      uses: actions/cache@v4
      with:
        path: node_modules
        key: npm-${{ runner.os }}-${{ inputs.renderer-template }}-${{ hashFiles('**/package.json') }}
        restore-keys: |
          npm-${{ runner.os }}-${{ inputs.renderer-template }}-

    - name: Install Dependencies
      run: >-
        if [ "${{ steps.check-renderer.outputs.RENDERER_EXIST }}" == "true" ] && [ -f "package-lock.json" ]; then
          npm ci
        else
          npm install
        fi
      shell: bash

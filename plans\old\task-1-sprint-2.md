# Task 1: Main Bible Reading Interface

**Sprint**: 2
**Priority**: High ⭐
**Estimated Time**: 3 days
**Status**: NOT STARTED

## Overview

Create the core Bible reading interface that serves as the primary user experience for <PERSON><PERSON><PERSON>. This task establishes the main reading view with smooth navigation, responsive design, and performance optimization for large chapters.

## Technical Implementation Details

### Component Architecture
```
ReadingView/
├── ReadingContainer.tsx          # Main container with layout
├── ChapterView.tsx              # Chapter display with verses
├── VerseComponent.tsx           # Individual verse rendering
├── NavigationBar.tsx            # Chapter/book navigation
├── ReadingProgress.tsx          # Progress indicators
└── KeyboardHandler.tsx          # Keyboard navigation logic
```

### State Management Integration
- Integrate with existing `readingStore` from Sprint 1
- Add reading position tracking
- Manage current book/chapter/verse state
- Handle navigation history

### Database Schema Requirements
```sql
-- Reading position tracking
CREATE TABLE reading_positions (
  id INTEGER PRIMARY KEY,
  translation_id TEXT NOT NULL, -- Foreign key to the translation's ID in the repositories table
  book_id INTEGER NOT NULL,
  chapter INTEGER NOT NULL,
  verse INTEGER NOT NULL,
  scroll_position REAL DEFAULT 0,
  last_read_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (translation_id) REFERENCES repositories(id)
);

-- Reading sessions for analytics
CREATE TABLE reading_sessions (
  id INTEGER PRIMARY KEY,
  start_time DATETIME DEFAULT CURRENT_TIMESTAMP,
  end_time DATETIME,
  verses_read INTEGER DEFAULT 0,
  chapters_read INTEGER DEFAULT 0
);
```

## Detailed Subtask Breakdown

### Subtask 1.1: Create Main Reading View Component (8 hours)
**Acceptance Criteria:**
- [ ] Responsive layout adapts to different screen sizes
- [ ] Clean typography with proper verse formatting
- [ ] Smooth scrolling performance
- [ ] Integration with theme system from Sprint 1

**Implementation Steps:**
1. Create `ReadingContainer` with responsive grid layout
2. Implement `ChapterView` with verse list rendering
3. Add proper typography and spacing
4. Integrate with existing theme provider

**Code Structure:**
```typescript
interface ReadingViewProps {
  translationId: string;
  bookId: number;
  chapter: number;
  initialVerse?: number;
}

const ReadingView: React.FC<ReadingViewProps> = ({
  translationId,
  bookId,
  chapter,
  initialVerse
}) => {
  // Implementation
};
```

### Subtask 1.2: Implement Smooth Scrolling and Virtualization (10 hours)
**Acceptance Criteria:**
- [ ] Handles chapters with 100+ verses without performance issues
- [ ] Smooth scrolling to specific verses
- [ ] Maintains scroll position on navigation
- [ ] Virtual scrolling for memory efficiency

**Technical Approach:**
- Use `react-window` or `react-virtualized` for large chapters
- Implement intersection observer for verse tracking
- Add smooth scroll behavior with `scrollIntoView`

**Potential Challenges:**
- Verse height variations affecting virtualization
- Maintaining scroll position across navigation
- Performance with complex verse formatting

**Mitigation Strategies:**
- Implement dynamic height calculation
- Store scroll positions in state management
- Use `React.memo` for verse components


> Status update: Deferred virtualization
>
> We attempted a VariableSizeList-based implementation (react-window) with dynamic row measurement. During manual testing, verse content with multiple lines and varied formatting led to clipping/overlap artifacts. To avoid UX regressions, we reverted to a simple scroll list for now. We'll revisit virtualization after core features are complete, and only if profiling indicates performance bottlenecks on large chapters.

### Subtask 1.3: Build Chapter/Verse Navigation (6 hours)
**Acceptance Criteria:**
- [ ] Navigation bar shows current location
- [ ] Quick navigation to any chapter/verse
- [ ] Breadcrumb navigation
- [ ] Previous/next chapter navigation

**Implementation Details:**
```typescript
interface NavigationState {
  currentBook: BookInfo;
  currentChapter: number;
  currentVerse: number;
  navigationHistory: NavigationItem[];
}
```

### Subtask 1.4: Add Verse Numbering and Formatting (4 hours)
**Acceptance Criteria:**
- [ ] Clear verse numbers that don't interfere with reading
- [ ] Proper paragraph and poetry formatting
- [ ] Responsive text sizing
- [ ] Accessibility compliance

**Formatting Requirements:**
- Verse numbers: Small, muted, non-selectable
- Poetry: Proper indentation and line breaks
- Paragraphs: Clear separation without verse number interruption
- Headers: Chapter titles and section headings

### Subtask 1.5: Create Reading Progress Indicators (4 hours)
**Acceptance Criteria:**
- [ ] Chapter progress bar
- [ ] Book completion percentage
- [ ] Reading session time tracking
- [ ] Verses read counter

**Progress Tracking:**
```typescript
interface ReadingProgress {
  currentPosition: {
    book: string;
    chapter: number;
    verse: number;
  };
  sessionStats: {
    startTime: Date;
    versesRead: number;
    timeSpent: number;
  };
  bookProgress: {
    chaptersRead: number;
    totalChapters: number;
    percentage: number;
  };
}
```

### Subtask 1.6: Implement Keyboard Navigation (6 hours)
**Acceptance Criteria:**
- [ ] Arrow keys navigate between verses
- [ ] Page Up/Down for chapter navigation
- [ ] Home/End for book navigation
- [ ] Keyboard shortcuts don't conflict with browser defaults

**Keyboard Shortcuts:**
- `↑/↓`: Previous/next verse
- `←/→`: Previous/next chapter
- `Page Up/Down`: Scroll by screen
- `Home/End`: First/last verse of chapter
- `Ctrl+Home/End`: First/last chapter of book

## Dependencies and Integration Points

### Sprint 1 Integration:
- **Database Service**: Query verses and chapters
- **Repository Store**: Access the currently active translation's data (`activeTranslationId`)
- **Theme System**: Apply consistent styling
- **Settings Provider**: Reading preferences
- **UI Store**: Navigation state management

### External Dependencies:
```json
{
  "react-intersection-observer": "^9.13.0",
  "react-window": "^1.8.8",
  "react-window-infinite-loader": "^1.0.9"
}
```

## Testing Considerations

### Unit Tests:
- [ ] Verse rendering with different content types
- [ ] Navigation state management
- [ ] Keyboard event handling
- [ ] Progress calculation accuracy

### Integration Tests:
- [ ] Database query integration
- [ ] Theme switching during reading
- [ ] Translation switching
- [ ] Settings changes affecting display

### Performance Tests:
- [ ] Large chapter rendering (Psalm 119)
- [ ] Memory usage with virtual scrolling
- [ ] Scroll performance benchmarks
- [ ] Navigation speed tests

## Time Breakdown

| Subtask | Estimated Hours | Priority |
|---------|----------------|----------|
| Main Reading View | 8 | High |
| Scrolling & Virtualization | 10 | High |
| Navigation | 6 | High |
| Verse Formatting | 4 | Medium |
| Progress Indicators | 4 | Medium |
| Keyboard Navigation | 6 | Medium |
| **Total** | **38 hours** | |

## Success Metrics

### Performance Targets:
- [ ] Initial render < 200ms
- [ ] Scroll to verse < 100ms
- [ ] Memory usage < 50MB for typical chapter
- [ ] 60fps scrolling performance

### User Experience Goals:
- [ ] Intuitive navigation without training
- [ ] Comfortable reading experience
- [ ] Responsive design on all screen sizes
- [ ] Accessibility compliance (WCAG 2.1 AA)

## Potential Challenges and Mitigation

### Challenge 1: Performance with Large Chapters
**Risk**: Psalm 119 (176 verses) causing lag
**Mitigation**: Implement virtual scrolling early, profile performance

### Challenge 2: Complex Verse Formatting
**Risk**: Poetry and prose formatting complexity
**Mitigation**: Create flexible verse component architecture

### Challenge 3: Cross-Platform Keyboard Handling
**Risk**: Different keyboard behaviors across platforms
**Mitigation**: Test on all target platforms, use platform detection

### Challenge 4: Responsive Design Complexity
**Risk**: Reading experience degrading on small screens
**Mitigation**: Mobile-first design approach, extensive device testing

## Definition of Done

- [ ] All subtasks completed and tested
- [ ] Performance targets met
- [ ] Responsive design verified on multiple devices
- [ ] Keyboard navigation fully functional
- [ ] Integration with Sprint 1 systems working
- [ ] Code reviewed and documented
- [ ] User acceptance testing passed

# Zaphnath Official Bible Repositories

This repository hosts copyright-free Bible repositories for the Zaphnath app.

The repositories here are verified and conform to the Zaphnath Bible Repository Standard (ZBRS) v1.x.

## Repository structure
- translation-id/
  - manifest.json
  - books/
  - README.md

## Discovery index
- Root `index.json` lists translations with: id, name, url, language, license, verified, last_updated, description, tags.

## Usage
- In the Zaphnath app, add this repository as a source by using the root manifest URL:
  https://raw.githubusercontent.com/beabzk/zbrs-official/main/manifest.json

- Alternatively, use the app's Discovery option which fetches from the official registry `zbrs-registry/index.json`.

## Licensing
- Only public‑domain (or equivalent) translations are included.
- License details and any jurisdiction-specific notes are documented in each translation’s `README.md` and `manifest.json`.

## Adding a translation
- Create a new `translation-id/` directory with content and `manifest.json` per ZBRS v1.
- Compute and set `technical.checksum` (sha256) and `technical.size_bytes`.
- Add an entry to the root `index.json` and to the discovery registry repository.
- Open a pull request to `beabzk/zbrs-official`.

## Publisher
- Zaphnath Official — https://github.com/beabzk/zbrs-official

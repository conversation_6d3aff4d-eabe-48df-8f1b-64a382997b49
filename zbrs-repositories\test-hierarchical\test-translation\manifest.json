{"zbrs_version": "1.0", "repository": {"id": "test-translation-1", "name": "Test Translation", "description": "A test translation for hierarchical imports.", "version": "1.0.0", "language": {"code": "eng", "name": "English", "direction": "ltr"}, "translation": {"type": "formal", "year": 2024, "copyright": "Public Domain", "license": "CC0", "source": "<PERSON><PERSON>nath Test Suite"}, "created_at": "2024-01-01T00:00:00Z", "updated_at": "2024-01-01T00:00:00Z"}, "content": {"books_count": 1, "testament": {"old": 1, "new": 0}, "books": [{"path": "01-GEN.json", "checksum": "placeholder-checksum"}], "features": {"audio": false, "cross_references": false, "footnotes": false, "study_notes": false}}, "technical": {"encoding": "UTF-8", "compression": "none", "checksum": "placeholder-checksum", "size_bytes": 0}}
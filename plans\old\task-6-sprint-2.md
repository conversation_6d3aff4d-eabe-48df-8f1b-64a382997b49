# Task 6: Reading History and Progress Tracking

**Sprint**: 2  
**Priority**: Low  
**Estimated Time**: 1 day  
**Status**: NOT STARTED

## Overview

Implement comprehensive reading history and progress tracking to help users understand their Bible reading patterns, maintain reading streaks, and continue where they left off. This feature enhances user engagement and provides valuable insights into reading habits.

## Technical Implementation Details

### Component Architecture
```
ReadingHistory/
├── HistoryContainer.tsx         # Main history interface
├── ReadingProgress.tsx         # Progress visualization
├── ReadingStats.tsx           # Statistics dashboard
├── ReadingStreak.tsx          # Streak tracking
├── ContinueReading.tsx        # Quick continue functionality
├── ReadingGoals.tsx           # Goal setting and tracking
└── hooks/
    ├── useReadingHistory.ts   # History management
    ├── useReadingStats.ts     # Statistics calculation
    └── useReadingGoals.ts     # Goal tracking
```

### State Management Integration
```typescript
interface ReadingHistoryState {
  currentSession: ReadingSession | null;
  recentSessions: ReadingSession[];
  readingStats: ReadingStatistics;
  readingGoals: ReadingGoal[];
  lastReadPosition: ReadingPosition | null;
}

interface ReadingSession {
  id: string;
  startTime: Date;
  endTime?: Date;
  duration: number; // in seconds
  versesRead: VerseReference[];
  chaptersCompleted: ChapterReference[];
  translationId: string;
  readingMode: 'sequential' | 'study' | 'search' | 'comparison';
}

interface ReadingStatistics {
  totalReadingTime: number;
  versesRead: number;
  chaptersCompleted: number;
  booksCompleted: number;
  currentStreak: number;
  longestStreak: number;
  averageSessionLength: number;
  readingVelocity: number; // verses per minute
  favoriteBooks: BookStatistic[];
  readingPatterns: {
    timeOfDay: TimeDistribution;
    dayOfWeek: DayDistribution;
    sessionLength: LengthDistribution;
  };
}

interface ReadingGoal {
  id: string;
  type: 'daily_time' | 'daily_verses' | 'weekly_chapters' | 'monthly_books' | 'yearly_bible';
  target: number;
  current: number;
  startDate: Date;
  endDate: Date;
  isActive: boolean;
}
```

### Database Schema Requirements
```sql
-- Reading sessions
CREATE TABLE reading_sessions (
  id TEXT PRIMARY KEY,
  translation_id TEXT NOT NULL,
  start_time DATETIME NOT NULL,
  end_time DATETIME,
  duration INTEGER DEFAULT 0, -- seconds
  verses_read TEXT, -- JSON array of verse references
  chapters_completed TEXT, -- JSON array of chapter references
  reading_mode TEXT DEFAULT 'sequential',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (translation_id) REFERENCES repositories(id)
);

-- Reading positions (for continue reading)
CREATE TABLE reading_positions (
  id INTEGER PRIMARY KEY,
  translation_id TEXT NOT NULL,
  book_id INTEGER NOT NULL,
  chapter INTEGER NOT NULL,
  verse INTEGER NOT NULL,
  scroll_position REAL DEFAULT 0,
  last_read_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  session_id TEXT,
  FOREIGN KEY (translation_id) REFERENCES repositories(id),
  FOREIGN KEY (session_id) REFERENCES reading_sessions(id)
);

-- Reading goals
CREATE TABLE reading_goals (
  id TEXT PRIMARY KEY,
  type TEXT NOT NULL,
  target INTEGER NOT NULL,
  current INTEGER DEFAULT 0,
  start_date DATE NOT NULL,
  end_date DATE NOT NULL,
  is_active BOOLEAN DEFAULT TRUE,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- Daily reading stats (for streak calculation)
CREATE TABLE daily_reading_stats (
  date DATE PRIMARY KEY,
  total_time INTEGER DEFAULT 0, -- seconds
  verses_read INTEGER DEFAULT 0,
  chapters_completed INTEGER DEFAULT 0,
  sessions_count INTEGER DEFAULT 0,
  goal_met BOOLEAN DEFAULT FALSE
);
```

## Detailed Subtask Breakdown

### Subtask 6.1: Implement Reading Session Tracking (6 hours)
**Acceptance Criteria:**
- [ ] Automatic session start/stop detection
- [ ] Accurate time tracking
- [ ] Verse and chapter progress recording
- [ ] Session persistence across app restarts
- [ ] Idle time detection and handling

**Session Management Implementation:**
```typescript
const useReadingSession = () => {
  const [currentSession, setCurrentSession] = useState<ReadingSession | null>(null);
  const [isIdle, setIsIdle] = useState(false);
  const idleTimeoutRef = useRef<NodeJS.Timeout>();
  
  const startSession = useCallback(async (translationId: string, mode: ReadingMode = 'sequential') => {
    const session: ReadingSession = {
      id: `session_${Date.now()}`,
      startTime: new Date(),
      duration: 0,
      versesRead: [],
      chaptersCompleted: [],
      translationId,
      readingMode: mode
    };
    
    await window.electronAPI.database.execute(
      'INSERT INTO reading_sessions (id, translation_id, start_time, reading_mode) VALUES (?, ?, ?, ?)',
      [session.id, translationId, session.startTime.toISOString(), mode]
    );
    
    setCurrentSession(session);
    resetIdleTimer();
  }, []);
  
  const endSession = useCallback(async () => {
    if (!currentSession) return;
    
    const endTime = new Date();
    const duration = Math.floor((endTime.getTime() - currentSession.startTime.getTime()) / 1000);
    
    await window.electronAPI.database.execute(
      'UPDATE reading_sessions SET end_time = ?, duration = ?, verses_read = ?, chapters_completed = ? WHERE id = ?',
      [
        endTime.toISOString(),
        duration,
        JSON.stringify(currentSession.versesRead),
        JSON.stringify(currentSession.chaptersCompleted),
        currentSession.id
      ]
    );
    
    // Update daily stats
    await updateDailyStats(endTime, duration, currentSession.versesRead.length);
    
    setCurrentSession(null);
    clearIdleTimer();
  }, [currentSession]);
  
  const recordVerseRead = useCallback((verseRef: VerseReference) => {
    if (!currentSession) return;
    
    setCurrentSession(prev => prev ? {
      ...prev,
      versesRead: [...prev.versesRead, verseRef]
    } : null);
    
    resetIdleTimer();
  }, [currentSession]);
  
  const resetIdleTimer = useCallback(() => {
    if (idleTimeoutRef.current) {
      clearTimeout(idleTimeoutRef.current);
    }
    
    setIsIdle(false);
    
    // Set idle after 5 minutes of inactivity
    idleTimeoutRef.current = setTimeout(() => {
      setIsIdle(true);
    }, 5 * 60 * 1000);
  }, []);
  
  const clearIdleTimer = useCallback(() => {
    if (idleTimeoutRef.current) {
      clearTimeout(idleTimeoutRef.current);
    }
  }, []);
  
  // Auto-end session on idle
  useEffect(() => {
    if (isIdle && currentSession) {
      endSession();
    }
  }, [isIdle, currentSession, endSession]);
  
  return {
    currentSession,
    isIdle,
    startSession,
    endSession,
    recordVerseRead,
    resetIdleTimer
  };
};
```

### Subtask 6.2: Create Reading History Interface (4 hours)
**Acceptance Criteria:**
- [ ] Chronological list of reading sessions
- [ ] Session details (time, duration, verses read)
- [ ] Filtering by date range and translation
- [ ] Search through reading history
- [ ] Export reading history

**History Display Component:**
```typescript
const ReadingHistory: React.FC = () => {
  const [sessions, setSessions] = useState<ReadingSession[]>([]);
  const [filters, setFilters] = useState<HistoryFilters>({
    dateRange: { start: subDays(new Date(), 30), end: new Date() },
    translations: [],
    minDuration: 0
  });
  
  const loadHistory = useCallback(async () => {
    const query = `
      SELECT rs.*, r.name as translation_name
      FROM reading_sessions rs
      JOIN repositories r ON rs.translation_id = r.id
      WHERE rs.start_time >= ? AND rs.start_time <= ?
      ${filters.translations.length > 0 ? 'AND rs.translation_id IN (' + filters.translations.map(() => '?').join(',') + ')' : ''}
      ${filters.minDuration > 0 ? 'AND rs.duration >= ?' : ''}
      ORDER BY rs.start_time DESC
    `;
    
    const params = [
      filters.dateRange.start.toISOString(),
      filters.dateRange.end.toISOString(),
      ...filters.translations,
      ...(filters.minDuration > 0 ? [filters.minDuration] : [])
    ];
    
    const results = await window.electronAPI.database.query(query, params);
    
    const sessionData = results.map(row => ({
      id: row.id,
      startTime: new Date(row.start_time),
      endTime: row.end_time ? new Date(row.end_time) : undefined,
      duration: row.duration,
      versesRead: JSON.parse(row.verses_read || '[]'),
      chaptersCompleted: JSON.parse(row.chapters_completed || '[]'),
      translationId: row.translation_id,
      translationName: row.translation_name,
      readingMode: row.reading_mode
    }));
    
    setSessions(sessionData);
  }, [filters]);
  
  useEffect(() => {
    loadHistory();
  }, [loadHistory]);
  
  return (
    <div className="reading-history">
      <HistoryFilters filters={filters} onChange={setFilters} />
      <div className="history-list">
        {sessions.map(session => (
          <SessionCard key={session.id} session={session} />
        ))}
      </div>
    </div>
  );
};
```

### Subtask 6.3: Build Reading Progress Visualization (4 hours)
**Acceptance Criteria:**
- [ ] Visual progress indicators for books/chapters
- [ ] Reading streak visualization
- [ ] Progress charts and graphs
- [ ] Goal progress indicators
- [ ] Interactive progress elements

**Progress Visualization:**
```typescript
const ReadingProgress: React.FC = () => {
  const { stats } = useReadingStats();
  const { goals } = useReadingGoals();
  
  return (
    <div className="reading-progress">
      <div className="progress-overview">
        <StatCard
          title="Current Streak"
          value={stats.currentStreak}
          unit="days"
          icon="flame"
          trend={stats.currentStreak > stats.averageStreak ? 'up' : 'down'}
        />
        <StatCard
          title="Verses Read"
          value={stats.versesRead}
          unit="verses"
          icon="book"
          trend="up"
        />
        <StatCard
          title="Reading Time"
          value={formatDuration(stats.totalReadingTime)}
          unit=""
          icon="clock"
          trend="up"
        />
      </div>
      
      <div className="progress-charts">
        <ReadingStreakChart streak={stats.currentStreak} history={stats.streakHistory} />
        <ReadingVelocityChart velocity={stats.readingVelocity} />
        <BookProgressChart books={stats.bookProgress} />
      </div>
      
      <div className="goal-progress">
        {goals.filter(g => g.isActive).map(goal => (
          <GoalProgressCard key={goal.id} goal={goal} />
        ))}
      </div>
    </div>
  );
};

const ReadingStreakChart: React.FC<{
  streak: number;
  history: StreakHistoryItem[];
}> = ({ streak, history }) => {
  const chartData = useMemo(() => {
    return history.map(item => ({
      date: format(item.date, 'MMM dd'),
      hasReading: item.hasReading,
      duration: item.duration
    }));
  }, [history]);
  
  return (
    <div className="streak-chart">
      <h3>Reading Streak: {streak} days</h3>
      <div className="streak-calendar">
        {chartData.map((day, index) => (
          <div
            key={index}
            className={`streak-day ${day.hasReading ? 'active' : 'inactive'}`}
            title={`${day.date}: ${day.hasReading ? formatDuration(day.duration) : 'No reading'}`}
          />
        ))}
      </div>
    </div>
  );
};
```

### Subtask 6.4: Add "Continue Reading" Functionality (3 hours)
**Acceptance Criteria:**
- [ ] Quick access to last reading position
- [ ] Multiple translation support
- [ ] Position restoration with scroll
- [ ] Reading context display
- [ ] Smart position suggestions

**Continue Reading Implementation:**
```typescript
const useContinueReading = () => {
  const [lastPositions, setLastPositions] = useState<ReadingPosition[]>([]);
  
  const updateReadingPosition = useCallback(async (
    repositoryId: string,
    bookId: number,
    chapter: number,
    verse: number,
    scrollPosition: number = 0
  ) => {
    const position: ReadingPosition = {
      repositoryId,
      bookId,
      chapter,
      verse,
      scrollPosition,
      lastReadAt: new Date()
    };
    
    await window.electronAPI.database.execute(
      `INSERT OR REPLACE INTO reading_positions 
       (repository_id, book_id, chapter, verse, scroll_position, last_read_at)
       VALUES (?, ?, ?, ?, ?, ?)`,
      [repositoryId, bookId, chapter, verse, scrollPosition, position.lastReadAt.toISOString()]
    );
    
    // Update local state
    setLastPositions(prev => {
      const filtered = prev.filter(p => p.repositoryId !== repositoryId);
      return [position, ...filtered].slice(0, 5); // Keep last 5 positions
    });
  }, []);
  
  const loadLastPositions = useCallback(async () => {
    const results = await window.electronAPI.database.query(
      `SELECT rp.*, r.name as repository_name, b.name as book_name
       FROM reading_positions rp
       JOIN repositories r ON rp.repository_id = r.id
       JOIN books b ON rp.book_id = b.id
       ORDER BY rp.last_read_at DESC
       LIMIT 5`
    );
    
    const positions = results.map(row => ({
      repositoryId: row.repository_id,
      repositoryName: row.repository_name,
      bookId: row.book_id,
      bookName: row.book_name,
      chapter: row.chapter,
      verse: row.verse,
      scrollPosition: row.scroll_position,
      lastReadAt: new Date(row.last_read_at)
    }));
    
    setLastPositions(positions);
  }, []);
  
  const continueReading = useCallback((position: ReadingPosition) => {
    // Navigate to the reading position
    // This would integrate with the navigation system from Task 1
    return {
      repositoryId: position.repositoryId,
      bookId: position.bookId,
      chapter: position.chapter,
      verse: position.verse,
      scrollPosition: position.scrollPosition
    };
  }, []);
  
  return {
    lastPositions,
    updateReadingPosition,
    loadLastPositions,
    continueReading
  };
};
```

### Subtask 6.5: Create Reading Statistics Dashboard (4 hours)
**Acceptance Criteria:**
- [ ] Comprehensive reading statistics
- [ ] Time-based analytics (daily, weekly, monthly)
- [ ] Reading pattern analysis
- [ ] Comparative statistics
- [ ] Export statistics functionality

**Statistics Calculation:**
```typescript
const useReadingStats = () => {
  const [stats, setStats] = useState<ReadingStatistics | null>(null);
  
  const calculateStats = useCallback(async () => {
    // Get all reading sessions
    const sessions = await window.electronAPI.database.query(
      'SELECT * FROM reading_sessions WHERE end_time IS NOT NULL ORDER BY start_time'
    );
    
    // Get daily stats for streak calculation
    const dailyStats = await window.electronAPI.database.query(
      'SELECT * FROM daily_reading_stats ORDER BY date DESC LIMIT 365'
    );
    
    const totalReadingTime = sessions.reduce((sum, session) => sum + session.duration, 0);
    const totalVersesRead = sessions.reduce((sum, session) => {
      const verses = JSON.parse(session.verses_read || '[]');
      return sum + verses.length;
    }, 0);
    
    // Calculate reading streak
    const currentStreak = calculateCurrentStreak(dailyStats);
    const longestStreak = calculateLongestStreak(dailyStats);
    
    // Calculate reading velocity (verses per minute)
    const readingVelocity = totalReadingTime > 0 ? (totalVersesRead / (totalReadingTime / 60)) : 0;
    
    // Analyze reading patterns
    const patterns = analyzeReadingPatterns(sessions);
    
    const statistics: ReadingStatistics = {
      totalReadingTime,
      versesRead: totalVersesRead,
      chaptersCompleted: calculateChaptersCompleted(sessions),
      booksCompleted: calculateBooksCompleted(sessions),
      currentStreak,
      longestStreak,
      averageSessionLength: sessions.length > 0 ? totalReadingTime / sessions.length : 0,
      readingVelocity,
      favoriteBooks: calculateFavoriteBooks(sessions),
      readingPatterns: patterns
    };
    
    setStats(statistics);
  }, []);
  
  const calculateCurrentStreak = (dailyStats: any[]): number => {
    let streak = 0;
    const today = format(new Date(), 'yyyy-MM-dd');
    
    for (const day of dailyStats) {
      if (day.date === today || day.goal_met) {
        streak++;
      } else {
        break;
      }
    }
    
    return streak;
  };
  
  const analyzeReadingPatterns = (sessions: any[]) => {
    const timeOfDay = new Array(24).fill(0);
    const dayOfWeek = new Array(7).fill(0);
    const sessionLengths: number[] = [];
    
    sessions.forEach(session => {
      const startTime = new Date(session.start_time);
      timeOfDay[startTime.getHours()]++;
      dayOfWeek[startTime.getDay()]++;
      sessionLengths.push(session.duration);
    });
    
    return {
      timeOfDay: timeOfDay.map((count, hour) => ({ hour, count })),
      dayOfWeek: dayOfWeek.map((count, day) => ({ day, count })),
      sessionLength: {
        average: sessionLengths.reduce((a, b) => a + b, 0) / sessionLengths.length,
        median: sessionLengths.sort()[Math.floor(sessionLengths.length / 2)],
        distribution: calculateDistribution(sessionLengths)
      }
    };
  };
  
  return { stats, calculateStats };
};
```

## Dependencies and Integration Points

### Sprint 1 Integration:
- **Database Service**: Store reading history and statistics
- **Settings Provider**: Reading tracking preferences
- **Repository Store**: Access repository information

### Task 1 Integration:
- **Reading View**: Track reading progress and position
- **Navigation**: Update position on navigation

### External Dependencies:
```json
{
  "date-fns": "^3.6.0",
  "recharts": "^2.12.7"
}
```

## Testing Considerations

### Unit Tests:
- [ ] Session tracking logic
- [ ] Statistics calculation
- [ ] Streak calculation
- [ ] Position tracking

### Integration Tests:
- [ ] Database persistence
- [ ] Cross-session continuity
- [ ] Goal tracking accuracy

### Performance Tests:
- [ ] Large history dataset handling
- [ ] Statistics calculation performance
- [ ] Real-time tracking overhead

## Time Breakdown

| Subtask | Estimated Hours | Priority |
|---------|----------------|----------|
| Session Tracking | 6 | High |
| History Interface | 4 | Medium |
| Progress Visualization | 4 | Medium |
| Continue Reading | 3 | High |
| Statistics Dashboard | 4 | Low |
| **Total** | **21 hours** | |

## Success Metrics

### Performance Targets:
- [ ] Session tracking overhead < 10ms
- [ ] Statistics calculation < 1s
- [ ] History loading < 500ms
- [ ] Position restoration < 200ms

### User Experience Goals:
- [ ] Motivating progress visualization
- [ ] Accurate reading tracking
- [ ] Useful continue reading feature
- [ ] Insightful statistics

## Potential Challenges and Mitigation

### Challenge 1: Accurate Time Tracking
**Risk**: Inaccurate reading time due to idle detection
**Mitigation**: Smart idle detection, user activity monitoring

### Challenge 2: Performance with Large History
**Risk**: Slow statistics calculation with extensive history
**Mitigation**: Efficient queries, data aggregation, caching

### Challenge 3: Privacy Concerns
**Risk**: Users concerned about reading tracking
**Mitigation**: Clear privacy controls, opt-in tracking, local storage only

## Definition of Done

- [ ] All subtasks completed and tested
- [ ] Session tracking accurate
- [ ] History interface functional
- [ ] Progress visualization working
- [ ] Continue reading operational
- [ ] Statistics calculation correct
- [ ] Performance targets met
- [ ] Privacy controls implemented

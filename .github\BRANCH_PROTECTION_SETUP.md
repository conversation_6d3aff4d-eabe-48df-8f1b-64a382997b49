# Branch Protection Setup

To configure branch protection rules for this repository, follow these steps:

## Main Branch Protection

1. Go to **Settings** → **Branches** in your GitHub repository
2. Click **Add rule** or edit the existing rule for `main`
3. Configure the following settings:

### Required Settings
- ✅ **Require a pull request before merging**
  - ✅ **Require approvals**: 1
  - ✅ **Dismiss stale PR approvals when new commits are pushed**
  - ✅ **Require review from code owners** (if you have a CODEOWNERS file)

- ✅ **Require status checks to pass before merging**
  - ✅ **Require branches to be up to date before merging**
  - Add these required status checks:
    - `typeckeck` (from compile-and-test workflow)
    - `compile / windows-latest`
    - `compile / ubuntu-latest` 
    - `compile / macos-latest`
    - `lint` (if you want to enforce linting)

### Optional Settings
- ✅ **Require conversation resolution before merging**
- ✅ **Require signed commits** (recommended for security)
- ✅ **Include administrators** (applies rules to repo admins too)

### Not Recommended
- ❌ **Require linear history** (can make collaboration harder)
- ❌ **Allow force pushes** (should stay disabled)
- ❌ **Allow deletions** (should stay disabled)

## Development Branch Protection (Optional)

If you use a `develop` branch, apply similar rules but with relaxed requirements:
- Require pull requests but allow self-approval
- Require status checks but fewer of them
- Allow force pushes for development work

## Release Branch Protection (Optional)

For `release/*` branches:
- Require pull requests with 1+ approvals
- Require all status checks
- Require conversation resolution
- Do not allow force pushes or deletions

## Notes

- The CI/CD workflow will automatically run on PRs and provide the required status checks
- Releases are triggered by pushing tags (v*), not by merging to main
- The lint workflow runs on PRs to catch issues early
- Dependabot PRs will require manual review due to these protection rules

# <PERSON><PERSON>nath Bible Reader - Comprehensive Project Plan

## Executive Summary

<PERSON><PERSON><PERSON> is a cross-platform desktop Bible reader application built with modern web technologies wrapped in Electron. The app emphasizes extensibility, offline-first functionality, and a superior reading experience with integrated audio support.

**Timeline**: 8-10 weeks (2-2.5 months)  
**Core Stack**: React + Vite + Electron + SQLite  
**Target Platforms**: Windows, macOS, Linux  
**Boilerplate**: [vite-electron-builder](https://github.com/cawa-93/vite-electron-builder) (monorepo structure)

## 1. Technical Architecture

### 1.1 High-Level Architecture

```
┌─────────────────────────────────────────────────────────────┐
│                    Electron Main Process                     │
│  ┌─────────────┐  ┌──────────────┐  ┌─────────────────┐   │
│  │ App Manager │  │ Window Manager│  │ Update Service  │   │
│  └─────────────┘  └──────────────┘  └─────────────────┘   │
│  ┌─────────────┐  ┌──────────────┐  ┌─────────────────┐   │
│  │ SQLite DB   │  │ File System  │  │ Audio Service   │   │
│  └─────────────┘  └──────────────┘  └─────────────────┘   │
└─────────────────────────┬───────────────────────────────────┘
                          │ IPC via contextBridge
┌─────────────────────────┴───────────────────────────────────┐
│                    Electron Renderer (React)                 │
│  ┌─────────────┐  ┌──────────────┐  ┌─────────────────┐   │
│  │ UI Components│  │ State Manager│  │ Service Layer   │   │
│  └─────────────┘  └──────────────┘  └─────────────────┘   │
└─────────────────────────────────────────────────────────────┘
```

### 1.2 Technology Stack Details

**Frontend (Renderer Package):**
(latest version where possible)
- React with TypeScript
- Vite
- TanStack Query for server state management
- <PERSON>ustand for client state management
- Tailwind CSS + shadcn/ui for styling
- Framer Motion for animations

**Backend/Electron (Main Package):**
- Electron with contextBridge security
- better-sqlite3 for local database (requires native module rebuilding)
- node-cache for in-memory caching
- electron-updater for auto-updates
- electron-store for user preferences

**Preload Package:**
- Secure IPC communication layer
- Type-safe API exposure via contextBridge
- Validation layer for all IPC calls

**Audio Stack:**
- Howler.js for audio playback (renderer)
- node-id3 for metadata parsing (main process)
- axios with progress tracking for downloads
- Custom audio cache manager in main process

**Localization & Search:**
- i18next for internationalization
- Fuse.js for fuzzy search
- Natural for advanced text processing

### 1.3 Security Architecture

The vite-electron-builder boilerplate enforces strict security practices:

**Preload Script Pattern:**
```typescript
// packages/preload/src/index.ts
export { sha256sum } from './nodeCrypto';
export { versions } from './versions';

// Custom APIs for Zaphnath
export const database = {
  query: async (sql: string, params: any[]) => {
    return await ipcRenderer.invoke('db:query', sql, params);
  },
  execute: async (sql: string, params: any[]) => {
    return await ipcRenderer.invoke('db:execute', sql, params);
  }
};

export const audio = {
  stream: (url: string) => ipcRenderer.invoke('audio:stream', url),
  download: (url: string, id: string) => ipcRenderer.invoke('audio:download', url, id),
  getProgress: (id: string) => ipcRenderer.invoke('audio:progress', id)
};

export const repository = {
  import: (path: string) => ipcRenderer.invoke('repo:import', path),
  validate: (data: any) => ipcRenderer.invoke('repo:validate', data),
  export: (id: string, path: string) => ipcRenderer.invoke('repo:export', id, path)
};
```

**Main Process Handlers:**
```typescript
// packages/main/src/security.ts
import { app, shell } from 'electron';
import { URL } from 'url';

// Restrict navigation and new window creation
export function setupSecurityRestrictions(win: BrowserWindow) {
  win.webContents.on('will-navigate', (event, url) => {
    const parsedUrl = new URL(url);
    if (parsedUrl.origin !== 'file://') {
      event.preventDefault();
    }
  });

  win.webContents.setWindowOpenHandler(({ url }) => {
    if (isSafeForExternalOpen(url)) {
      shell.openExternal(url);
    }
    return { action: 'deny' };
  });
}
```

**Content Security Policy:**
- Strict CSP headers in production
- No inline scripts or styles
- Only local resources and whitelisted CDNs
- No eval() or new Function()

## 2. Project Structure

```
zaphnath/
├── packages/
│   ├── main/              # Electron main process
│   │   ├── src/
│   │   │   ├── index.ts
│   │   │   ├── services/
│   │   │   │   ├── database/
│   │   │   │   │   ├── connection.ts
│   │   │   │   │   ├── migrations.ts
│   │   │   │   │   └── queries.ts
│   │   │   │   ├── audio/
│   │   │   │   │   ├── audio-manager.ts
│   │   │   │   │   ├── cache-manager.ts
│   │   │   │   │   └── download-queue.ts
│   │   │   │   ├── repository/
│   │   │   │   │   ├── repository-manager.ts
│   │   │   │   │   ├── validator.ts
│   │   │   │   │   └── importer.ts
│   │   │   │   └── updater.ts
│   │   │   ├── security.ts
│   │   │   └── mainWindow.ts
│   │   ├── package.json
│   │   ├── tsconfig.json
│   │   └── vite.config.js
│   │
│   ├── preload/           # Secure bridge between main and renderer
│   │   ├── src/
│   │   │   ├── index.ts
│   │   │   ├── apis/
│   │   │   │   ├── database.ts
│   │   │   │   ├── audio.ts
│   │   │   │   ├── repository.ts
│   │   │   │   └── system.ts
│   │   │   └── validation.ts
│   │   ├── package.json
│   │   ├── tsconfig.json
│   │   └── vite.config.js
│   │
│   ├── renderer/          # React app (created via npm run init)
│   │   ├── src/
│   │   │   ├── App.tsx
│   │   │   ├── main.tsx
│   │   │   ├── components/
│   │   │   │   ├── reader/
│   │   │   │   │   ├── BibleReader.tsx
│   │   │   │   │   ├── VerseDisplay.tsx
│   │   │   │   │   └── ComparisonView.tsx
│   │   │   │   ├── navigation/
│   │   │   │   ├── search/
│   │   │   │   └── audio/
│   │   │   ├── features/
│   │   │   │   ├── bible-reader/
│   │   │   │   ├── notes/
│   │   │   │   ├── bookmarks/
│   │   │   │   └── reading-plans/
│   │   │   ├── hooks/
│   │   │   ├── stores/
│   │   │   ├── services/
│   │   │   └── types/
│   │   ├── index.html
│   │   ├── package.json
│   │   ├── tsconfig.json
│   │   └── vite.config.js
│   │
│   └── electron-versions/ # Helper package from boilerplate
│       └── src/
│
├── resources/             # App resources
│   ├── icons/
│   └── schemas/
│       └── repository-v1.schema.json
│
├── scripts/               # Build and development scripts
├── tests/                 # Test files
│   ├── e2e/
│   │   └── app.spec.ts
│   └── unit/
│
├── buildResources/        # Electron-builder resources
│   └── icon.png
│
├── .github/
│   └── workflows/
│       ├── ci.yml
│       └── release.yml
│
├── .electron-vendors.cache.json
├── electron-builder.yml
├── package.json
├── tsconfig.json
└── .eslintrc.json
```

## 3. Repository System Design

### 3.1 Repository Schema (ZBRS v1.0)

The Zaphnath Bible Repository Standard (ZBRS) v1.0 uses a structure composed of two main manifest types: a **Parent Repository Manifest** and a **Translation Manifest**.

#### 3.1.1 Parent Repository Manifest (`manifest.json` at root)

This file defines a collection of Bible translations.

```json
{
  "zbrs_version": "1.0",
  "repository": {
    "id": "uuid-for-the-collection",
    "name": "Zaphnath Official Repository",
    "description": "A collection of verified Bible translations.",
    "version": "1.0.0",
    "type": "parent",
    "created_at": "2024-01-01T00:00:00Z",
    "updated_at": "2024-01-01T00:00:00Z"
  },
  "publisher": {},
  "translations": [
    {
      "id": "kjv-1769",
      "name": "King James Version (1769)",
      "directory": "kjv-1769",
      "language": { "code": "en", "name": "English" },
      "status": "active"
    }
  ],
  "technical": {}
}
```

#### 3.1.2 Translation Manifest (`manifest.json` in subdirectory)

This file defines a single Bible translation and its content.

```json
{
  "zbrs_version": "1.0",
  "repository": {
    "id": "kjv-1769",
    "name": "King James Version (1769)",
    "description": "The KJV translation.",
    "version": "1.0.0",
    "created_at": "2024-01-01T00:00:00Z",
    "updated_at": "2024-01-01T00:00:00Z"
  },
  "content": {
    "format": "JSON",
    "books": [
      {
        "id": "GEN",
        "name": "Genesis",
        "chapters": 50,
        "dataPath": "./books/GEN.json"
      }
    ]
  },
  "technical": {}
}
```

### 3.2 Database Schema

The database schema is designed to support the hierarchical repository structure where repositories are collections containing multiple translations.

```sql
-- Core table for both repositories (parents) and translations (children)
CREATE TABLE repositories (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT,
    type TEXT NOT NULL CHECK (type IN ('parent', 'translation')),
    parent_id TEXT, -- NULL for parent repositories, parent repo ID for translations
    language_code TEXT, -- Only for translation type
    language_name TEXT, -- Only for translation type
    version TEXT NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    imported_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    metadata JSON NOT NULL,
    FOREIGN KEY (parent_id) REFERENCES repositories(id) ON DELETE CASCADE
);

-- Translation status and relationship tracking
CREATE TABLE repository_translations (
    id TEXT PRIMARY KEY,
    parent_repository_id TEXT NOT NULL,
    translation_repository_id TEXT NOT NULL,
    directory_name TEXT NOT NULL,
    status TEXT DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'importing')),
    import_selected BOOLEAN DEFAULT TRUE,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (parent_repository_id) REFERENCES repositories(id) ON DELETE CASCADE,
    FOREIGN KEY (translation_repository_id) REFERENCES repositories(id) ON DELETE CASCADE,
    UNIQUE(parent_repository_id, translation_repository_id)
);

-- Content tables, linked to a specific translation repository
CREATE TABLE books (
    id TEXT PRIMARY KEY,
    repository_id TEXT NOT NULL, -- Points to translation repository (type='translation')
    book_code TEXT NOT NULL,
    name TEXT NOT NULL,
    abbreviation TEXT,
    testament TEXT CHECK (testament IN ('OT', 'NT')),
    book_order INTEGER,
    chapter_count INTEGER NOT NULL,
    metadata JSON,
    FOREIGN KEY (repository_id) REFERENCES repositories(id) ON DELETE CASCADE
);

CREATE TABLE verses (
    id TEXT PRIMARY KEY,
    repository_id TEXT NOT NULL, -- Points to translation repository (type='translation')
    book_id TEXT NOT NULL,
    chapter INTEGER NOT NULL,
    verse INTEGER NOT NULL,
    text TEXT NOT NULL,
    metadata JSON,
    FOREIGN KEY (repository_id) REFERENCES repositories(id) ON DELETE CASCADE,
    FOREIGN KEY (book_id) REFERENCES books(id) ON DELETE CASCADE
);

-- Other tables (audio, bookmarks, notes, etc.) remain largely the same,
-- but their `repository_id` will always point to a translation's ID (type='translation').

-- Indexes
CREATE INDEX idx_repositories_parent ON repositories(parent_id);
CREATE INDEX idx_repositories_type ON repositories(type);
CREATE INDEX idx_repository_translations_parent ON repository_translations(parent_repository_id);
CREATE INDEX idx_books_repository ON books(repository_id);
CREATE INDEX idx_verses_location ON verses(repository_id, book_id, chapter, verse);
```

## 4. Development Milestones

### Sprint 0: Preparation & Setup (Week 1)
**Duration**: 5 days

**Tasks:**
- [ ] Clone vite-electron-builder boilerplate
- [ ] Run `npm run init` to create React renderer package
- [ ] Configure TypeScript for strict mode across all packages
- [ ] Set up Tailwind CSS and shadcn/ui in renderer
- [ ] Configure better-sqlite3 with electron-rebuild
- [ ] Set up database in app userData directory
- [ ] Create base IPC handlers following boilerplate pattern
- [ ] Configure CI/CD pipeline with GitHub Actions

**Key Boilerplate Integration Steps:**
1. Use `npm run init` to generate renderer package
2. Modify `packages/main/src/mainWindow.ts` for custom window settings
3. Extend preload APIs in `packages/preload/src/index.ts`
4. Follow the boilerplate's security model strictly

**Deliverables:**
- Working development environment with hot reload
- Basic app shell that builds and runs
- CI/CD pipeline for automated testing
- Successfully integrated better-sqlite3

### Sprint 1: Core Foundation & Repository Management (Week 2-3)
**Duration**: 10 days

**Tasks:**
- [x] Implement secure IPC communication layer (COMPLETED in Sprint 0)
- [x] Create database service with migration system (COMPLETED in Sprint 0)
- [ ] Build basic UI layout and navigation
- [ ] Implement theme system (dark/light mode)
- [ ] Create settings/preferences system
- [ ] Set up i18next for internationalization
- [ ] Implement basic state management with Zustand
- [ ] Create logging and error handling system
- [ ] Build repository management UI for ZBRS repositories
- [ ] Implement repository import/validation UI
- [ ] Create repository switching interface
- [ ] Add repository status and metadata display

**Deliverables:**
- Functional app skeleton with navigation
- Working database layer (COMPLETED)
- Theme switching capability
- Basic preferences storage
- Repository management interface
- ZBRS repository import functionality

### Sprint 2: Bible Reader Core (Week 4-5)
**Duration**: 10 days

**Tasks:**
- [x] Design and implement repository schema validator (COMPLETED in Sprint 0 - ZBRS)
- [x] Create repository import/export functionality (COMPLETED in Sprint 0 - ZBRS)
- [ ] **MOVED FROM SPRINT 3**: Create main reading view with smooth scrolling
- [ ] **MOVED FROM SPRINT 3**: Implement chapter/verse navigation
- [ ] **MOVED FROM SPRINT 3**: Build verse selection and highlighting
- [ ] **MOVED FROM SPRINT 3**: Add font size and line spacing controls
- [ ] **MOVED FROM SPRINT 3**: Create reading progress tracking
- [ ] **MOVED FROM SPRINT 3**: Implement keyboard shortcuts
- [ ] Build verse comparison view
- [ ] Add repository update checking

**Deliverables:**
- Working repository import system (COMPLETED)
- Fully functional Bible reader interface
- Verse comparison functionality
- Customizable reading experience

### Sprint 3: Advanced Reading Features (Week 5-6)
**Duration**: 10 days

**Tasks:**
- [ ] **MOVED FROM SPRINT 2**: Add verse copying functionality
- [ ] **MOVED FROM SPRINT 2**: Build print support
- [ ] **MOVED FROM SPRINT 5**: Implement Fuse.js for fuzzy search
- [ ] **MOVED FROM SPRINT 5**: Create advanced search UI with filters
- [ ] **MOVED FROM SPRINT 5**: Build search history
- [ ] Add cross-references display
- [ ] Implement verse linking and references
- [ ] Create reading history tracking
- [ ] Add verse sharing functionality

**Deliverables:**
- Advanced search functionality
- Cross-reference system
- Verse sharing and copying
- Print support

### Sprint 4: Audio System (Week 7)
**Duration**: 5 days

**Tasks:**
- [ ] Integrate Howler.js for audio playback
- [ ] Create audio player UI controls
- [ ] Implement streaming with cache management
- [ ] Build download manager with progress tracking
- [ ] Create cache eviction algorithm
- [ ] Add audio synchronization with text
- [ ] Implement playback speed control
- [ ] Add audio error handling and retry logic

**Deliverables:**
- Working audio playback system
- Download management functionality
- Synchronized audio-text experience

### Sprint 5: Search & User Features (Week 8)
**Duration**: 5 days

**Tasks:**
- [ ] Implement Fuse.js for fuzzy search
- [ ] Create advanced search UI with filters
- [ ] Build bookmarks system with categories
- [ ] Implement notes with rich text editor
- [ ] Create reading plans functionality
- [ ] Add cross-references display
- [ ] Build search history
- [ ] Implement bulk operations (export notes, etc.)

**Deliverables:**
- Powerful search functionality
- Complete bookmarks and notes system
- Reading plans feature

### Sprint 6: Polish & Optimization (Week 9)
**Duration**: 5 days

**Tasks:**
- [ ] Optimize bundle size and startup time
- [ ] Implement lazy loading for components
- [ ] Add loading states and skeletons
- [ ] Create onboarding flow
- [ ] Build keyboard shortcuts guide
- [ ] Add telemetry (with user consent)
- [ ] Optimize database queries
- [ ] Implement data backup/restore

**Deliverables:**
- Optimized performance
- Polished user experience
- Complete feature set

### Sprint 7: Testing & Packaging (Week 10)
**Duration**: 5 days

**Tasks:**
- [ ] Write comprehensive unit tests
- [ ] Create E2E tests with Playwright
- [ ] Set up code signing certificates
- [ ] Configure auto-updater
- [ ] Create installers for all platforms
- [ ] Write user documentation
- [ ] Perform security audit
- [ ] Beta testing with small user group

**Deliverables:**
- Fully tested application
- Signed installers for all platforms
- Complete documentation

## 5. Key Libraries & Dependencies

### Core Dependencies (Root package.json)
```json
{
  "devDependencies": {
    "playwright": "^1.45.0",
    "electron": "^37.2.5",
    "electron-builder": "^24.13.0",
    "vite": "^5.3.0",
    "typescript": "^5.5.0"
  }
}
```

### Main Process Dependencies
```json
{
  "dependencies": {
    "electron-updater": "^6.2.0",
    "better-sqlite3": "^11.1.0",
    "electron-store": "^10.0.0",
    "node-cache": "^5.1.2",
    "node-id3": "^0.2.6",
    "axios": "^1.7.0",
    "zod": "^3.23.0"
  }
}
```

### Renderer Dependencies
```json
{
  "dependencies": {
    "react": "^19.0.0",
    "react-dom": "^19.0.0",
    "@tanstack/react-query": "^5.51.0",
    "zustand": "^4.5.0",
    "howler": "^2.2.4",
    "fuse.js": "^7.0.0",
    "i18next": "^23.12.0",
    "react-i18next": "^15.0.0",
    "tailwindcss": "^3.4.0",
    "@radix-ui/react-*": "latest",
    "framer-motion": "^11.3.0",
    "date-fns": "^3.6.0",
    "react-intersection-observer": "^9.13.0",
    "@tiptap/react": "^2.5.0",
    "@tiptap/starter-kit": "^2.5.0"
  }
}
```

### Important Notes on better-sqlite3
- **Native Module**: Requires rebuilding for Electron
- **Build Configuration**: Add to electron-builder config:
```yaml
npmRebuild: true
buildDependenciesFromSource: true
nodeGypRebuild: false
```
- **Preload Usage**: Database operations must be in main process only
- **IPC Pattern**: Use invoke/handle pattern for all database operations

## 6. Potential Challenges & Solutions

### Challenge 1: Large Bible Text Data
**Problem**: Loading entire Bible versions can consume significant memory.
**Solution**: 
- Implement virtual scrolling for large chapters
- Load verses on-demand with intelligent prefetching
- Use database indexing for fast queries
- Cache frequently accessed passages

### Challenge 2: Audio File Management
**Problem**: Audio files are large and numerous (1189 chapters).
**Solution**:
- Implement progressive downloading
- Smart cache management with LRU eviction
- Allow users to download by book/testament
- Compress audio metadata in database

### Challenge 3: Cross-Platform Compatibility
**Problem**: Different OS behaviors and file systems.
**Solution**:
- Use electron's app.getPath() for consistent paths
- Test file operations on all platforms
- Handle platform-specific keyboard shortcuts
- Use normalized path separators

### Challenge 4: Bundle Size
**Problem**: Electron apps tend to be large.
**Solution**:
- Use electron-builder's compression
- Lazy load heavy components
- Tree-shake unused imports
- Consider shipping without default Bible data
- Use code splitting for features

### Challenge 5: Search Performance
**Problem**: Searching through multiple Bible versions can be slow.
**Solution**:
- Use SQLite's FTS5 for full-text search
- Pre-build search indexes
- Implement search result pagination
- Add search query caching

## 7. Performance Targets

- **Startup Time**: < 3 seconds to interactive
- **Search Response**: < 100ms for simple searches
- **Audio Start**: < 500ms from click to playback
- **Memory Usage**: < 200MB for base app
- **Bundle Size**: < 80MB compressed installer

## 8. Security Considerations

1. **Content Security Policy**: Strict CSP headers
2. **Context Isolation**: All IPC through contextBridge
3. **Input Validation**: Sanitize all user inputs
4. **Secure Updates**: Code-signed auto-updates only
5. **Data Encryption**: Encrypt sensitive user data
6. **Network Security**: HTTPS only for remote resources

## 9. Success Criteria

- [ ] Supports 5+ Bible versions simultaneously
- [ ] Audio playback works offline after download
- [ ] Search returns results in < 100ms
- [ ] Supports 10+ UI languages
- [ ] Memory usage stays under 300MB with 3 versions loaded
- [ ] 95% crash-free rate
- [ ] Auto-updates work seamlessly
- [ ] Accessibility score > 95

## 10. Boilerplate-Specific Considerations

### Development Workflow
1. **Hot Module Replacement**: The boilerplate provides HMR for all packages
2. **Type Safety**: Full TypeScript support across IPC boundaries
3. **Auto-imports**: Preload APIs are automatically exposed to renderer via ES modules

### Building & Distribution
1. **Monorepo Build**: `npm run compile` builds all packages in correct order
2. **Code Signing**: Pre-configured in `.github/workflows/release.yml`
3. **Auto-update**: Works out-of-box with GitHub Releases

### Key Differences from Standard Electron
1. **No direct contextBridge**: The boilerplate handles exposure automatically
2. **ES Modules**: Full ESM support, no CommonJS mixing
3. **Vite Integration**: Each package has its own vite.config.js

### Migration Notes
- Database operations must be in main process only
- File system access through preload APIs
- Renderer imports from '@app/preload' not global objects
- Security restrictions are pre-configured and strict

## 11. Post-Launch Roadmap

**Version 1.1 (Month 3)**
- Cloud sync for notes and bookmarks
- Bible study tools (commentaries, dictionaries)
- Social sharing features

**Version 1.2 (Month 4)**
- Mobile companion app sync
- Advanced study features (word studies, parsing)
- Plugin system for third-party extensions

**Version 2.0 (Month 6)**
- AI-powered study assistant
- Collaborative study sessions
- Advanced visualization tools

## Conclusion

This comprehensive plan provides a clear path to building a robust, extensible Bible reader application using the vite-electron-builder boilerplate. The monorepo architecture ensures maintainability, while the phased approach allows for iterative development and testing. With careful attention to the boilerplate's security model and proper integration of native modules like better-sqlite3, Zaphnath can become a premier Bible study tool for desktop users.
# Task 3: Search Functionality with Fu<PERSON>.js

**Sprint**: 2  
**Priority**: High ⭐  
**Estimated Time**: 3 days  
**Status**: NOT STARTED

## Overview

Implement comprehensive search functionality using Fuse.js for fuzzy search across all loaded repositories. This task creates a powerful search system with filtering, result highlighting, search history, and advanced search options that form the backbone of Bible study capabilities.

## Technical Implementation Details

### Component Architecture
```
Search/
├── SearchContainer.tsx          # Main search interface
├── SearchInput.tsx             # Search input with autocomplete
├── SearchFilters.tsx           # Book, testament, translation filters
├── SearchResults.tsx           # Results display with pagination
├── SearchResultItem.tsx        # Individual result component
├── SearchHistory.tsx           # Recent searches
├── AdvancedSearch.tsx          # Advanced search options
└── hooks/
    ├── useSearch.ts            # Main search logic
    ├── useSearchHistory.ts     # Search history management
    └── useSearchFilters.ts     # Filter state management
```

### Search Index Architecture
```typescript
interface SearchIndex {
  verses: VerseSearchItem[];
  fuseInstance: Fuse<VerseSearchItem>;
  lastUpdated: Date;
  translationId: string;
}

interface VerseSearchItem {
  id: string;
  translationId: string;
  bookId: number;
  bookName: string;
  chapter: number;
  verse: number;
  text: string;
  testament: 'old' | 'new';
  searchableText: string; // Normalized for search
}
```

### Database Schema Requirements
```sql
-- Search history
CREATE TABLE search_history (
  id INTEGER PRIMARY KEY,
  query TEXT NOT NULL,
  filters TEXT, -- JSON string of applied filters
  result_count INTEGER,
  timestamp DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- Search index cache (for performance)
CREATE TABLE search_index_cache (
  translation_id TEXT PRIMARY KEY, -- The ID of the translation
  index_data TEXT, -- Serialized search index
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (translation_id) REFERENCES repositories(id)
);

-- Popular searches for suggestions
CREATE TABLE search_suggestions (
  id INTEGER PRIMARY KEY,
  query TEXT UNIQUE NOT NULL,
  frequency INTEGER DEFAULT 1,
  last_used DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

## Detailed Subtask Breakdown

### Subtask 3.1: Integrate Fuse.js for Fuzzy Search (10 hours)
**Acceptance Criteria:**
- [ ] Fuse.js integrated with optimized configuration
- [ ] A separate search index is built for each translation
- [ ] Fuzzy matching with relevance scoring
- [ ] Search performance under 500ms for typical queries
- [ ] Index updates when translations are added, removed, or updated

**Fuse.js Configuration:**
```typescript
const fuseOptions: Fuse.IFuseOptions<VerseSearchItem> = {
  keys: [
    {
      name: 'searchableText',
      weight: 1.0
    },
    {
      name: 'bookName',
      weight: 0.3
    }
  ],
  threshold: 0.4, // Fuzzy matching sensitivity
  distance: 100,
  minMatchCharLength: 2,
  includeScore: true,
  includeMatches: true,
  ignoreLocation: true
};
```

**Search Index Management:**
```typescript
class SearchIndexManager {
  private indexes: Map<string, SearchIndex> = new Map();
  
  async buildIndex(translationId: string): Promise<SearchIndex> {
    const verses = await this.loadVerses(translationId);
    const searchItems = verses.map(verse => ({
      ...verse,
      searchableText: this.normalizeText(verse.text)
    }));
    
    const fuseInstance = new Fuse(searchItems, fuseOptions);
    
    const index: SearchIndex = {
      verses: searchItems,
      fuseInstance,
      lastUpdated: new Date(),
      translationId
    };
    
    this.indexes.set(translationId, index);
    await this.cacheIndex(translationId, index);
    
    return index;
  }
  
  private normalizeText(text: string): string {
    return text
      .toLowerCase()
      .replace(/[^\w\s]/g, ' ')
      .replace(/\s+/g, ' ')
      .trim();
  }
}
```

### Subtask 3.2: Create Search UI with Filters (8 hours)
**Acceptance Criteria:**
- [ ] Responsive search interface
- [ ] Real-time search suggestions
- [ ] Filter by book, testament, translation
- [ ] Clear visual hierarchy
- [ ] Keyboard navigation support

**Search Interface Components:**
```typescript
interface SearchFilters {
  books: string[];
  testament: 'all' | 'old' | 'new';
  translations: string[];
  dateRange?: {
    start: Date;
    end: Date;
  };
}

const SearchContainer: React.FC = () => {
  const [query, setQuery] = useState('');
  const [filters, setFilters] = useState<SearchFilters>({
    books: [],
    testament: 'all',
    translations: []
  });
  
  const { results, isLoading, error } = useSearch(query, filters);
  
  return (
    <div className="search-container">
      <SearchInput 
        value={query}
        onChange={setQuery}
        placeholder="Search the Bible..."
      />
      <SearchFilters 
        filters={filters}
        onChange={setFilters}
      />
      <SearchResults 
        results={results}
        isLoading={isLoading}
        error={error}
      />
    </div>
  );
};
```

### Subtask 3.3: Implement Search Result Highlighting (6 hours)
**Acceptance Criteria:**
- [ ] Search terms highlighted in results
- [ ] Context around matches shown
- [ ] Multiple match highlighting
- [ ] Snippet generation with ellipsis
- [ ] Highlight styling matches theme

**Result Highlighting Logic:**
```typescript
const highlightMatches = (text: string, matches: Fuse.FuseResultMatch[]): React.ReactNode => {
  if (!matches || matches.length === 0) return text;
  
  const highlights: Array<{start: number, end: number}> = [];
  
  matches.forEach(match => {
    if (match.indices) {
      match.indices.forEach(([start, end]) => {
        highlights.push({start, end: end + 1});
      });
    }
  });
  
  // Sort and merge overlapping highlights
  const mergedHighlights = mergeOverlappingRanges(highlights);
  
  return renderHighlightedText(text, mergedHighlights);
};

const generateSnippet = (text: string, matches: Fuse.FuseResultMatch[], maxLength: number = 200): string => {
  if (!matches || matches.length === 0) {
    return text.length > maxLength ? text.substring(0, maxLength) + '...' : text;
  }
  
  const firstMatch = matches[0];
  const matchStart = firstMatch.indices?.[0]?.[0] || 0;
  
  const snippetStart = Math.max(0, matchStart - 50);
  const snippetEnd = Math.min(text.length, snippetStart + maxLength);
  
  let snippet = text.substring(snippetStart, snippetEnd);
  
  if (snippetStart > 0) snippet = '...' + snippet;
  if (snippetEnd < text.length) snippet = snippet + '...';
  
  return snippet;
};
```

### Subtask 3.4: Build Search History (4 hours)
**Acceptance Criteria:**
- [ ] Recent searches stored and displayed
- [ ] Search history persistence
- [ ] Quick re-run of previous searches
- [ ] Search history clearing option
- [ ] Popular searches suggestions

**Search History Management:**
```typescript
const useSearchHistory = () => {
  const [history, setHistory] = useState<SearchHistoryItem[]>([]);
  
  const addToHistory = useCallback(async (query: string, filters: SearchFilters, resultCount: number) => {
    const historyItem: SearchHistoryItem = {
      query,
      filters,
      resultCount,
      timestamp: new Date()
    };
    
    await window.electronAPI.database.execute(
      'INSERT INTO search_history (query, filters, result_count) VALUES (?, ?, ?)',
      [query, JSON.stringify(filters), resultCount]
    );
    
    setHistory(prev => [historyItem, ...prev.slice(0, 19)]); // Keep last 20
  }, []);
  
  const loadHistory = useCallback(async () => {
    const results = await window.electronAPI.database.query(
      'SELECT * FROM search_history ORDER BY timestamp DESC LIMIT 20'
    );
    
    setHistory(results.map(row => ({
      query: row.query,
      filters: JSON.parse(row.filters || '{}'),
      resultCount: row.result_count,
      timestamp: new Date(row.timestamp)
    })));
  }, []);
  
  return { history, addToHistory, loadHistory };
};
```

### Subtask 3.5: Add Advanced Search Options (6 hours)
**Acceptance Criteria:**
- [ ] Exact phrase search with quotes
- [ ] Exclude words with minus operator
- [ ] Boolean operators (AND, OR, NOT)
- [ ] Wildcard search support
- [ ] Regular expression search option

**Advanced Search Parser:**
```typescript
interface ParsedQuery {
  includeTerms: string[];
  excludeTerms: string[];
  exactPhrases: string[];
  wildcardTerms: string[];
  booleanQuery?: BooleanQuery;
}

const parseAdvancedQuery = (query: string): ParsedQuery => {
  const result: ParsedQuery = {
    includeTerms: [],
    excludeTerms: [],
    exactPhrases: [],
    wildcardTerms: []
  };
  
  // Extract exact phrases (quoted text)
  const phraseRegex = /"([^"]+)"/g;
  let match;
  while ((match = phraseRegex.exec(query)) !== null) {
    result.exactPhrases.push(match[1]);
    query = query.replace(match[0], '');
  }
  
  // Extract exclude terms (prefixed with -)
  const excludeRegex = /-(\w+)/g;
  while ((match = excludeRegex.exec(query)) !== null) {
    result.excludeTerms.push(match[1]);
    query = query.replace(match[0], '');
  }
  
  // Extract wildcard terms (containing *)
  const wildcardRegex = /(\w*\*\w*)/g;
  while ((match = wildcardRegex.exec(query)) !== null) {
    result.wildcardTerms.push(match[1]);
    query = query.replace(match[0], '');
  }
  
  // Remaining terms are include terms
  result.includeTerms = query.trim().split(/\s+/).filter(term => term.length > 0);
  
  return result;
};
```

### Subtask 3.6: Create Search Result Export (4 hours)
**Acceptance Criteria:**
- [ ] Export results to multiple formats (CSV, JSON, TXT)
- [ ] Include verse references and context
- [ ] Batch export with progress indication
- [ ] Export with applied filters information
- [ ] Custom export templates

**Export Functionality:**
```typescript
interface ExportOptions {
  format: 'csv' | 'json' | 'txt' | 'pdf';
  includeContext: boolean;
  includeFilters: boolean;
  maxResults?: number;
}

const exportSearchResults = async (
  results: SearchResult[],
  query: string,
  options: ExportOptions
): Promise<void> => {
  const exportData = results.map(result => ({
    reference: `${result.bookName} ${result.chapter}:${result.verse}`,
    text: result.text,
    context: options.includeContext ? result.context : undefined,
    score: result.score
  }));
  
  switch (options.format) {
    case 'csv':
      await exportToCSV(exportData, query);
      break;
    case 'json':
      await exportToJSON(exportData, query, options);
      break;
    case 'txt':
      await exportToText(exportData, query);
      break;
    case 'pdf':
      await exportToPDF(exportData, query);
      break;
  }
};
```

## Dependencies and Integration Points

### Sprint 1 Integration:
- **Database Service**: Query verses for search index
- **Repository Store**: Access repository data
- **Settings Provider**: Search preferences
- **UI Store**: Search state management

### Task 1 Integration:
- **Reading View**: Navigate to search results
- **Navigation**: Integrate search with reading navigation

### External Dependencies:
```json
{
  "fuse.js": "^7.0.0",
  "react-hotkeys-hook": "^4.4.1",
  "react-window": "^1.8.8",
  "papaparse": "^5.4.1"
}
```

## Testing Considerations

### Unit Tests:
- [ ] Search query parsing
- [ ] Result highlighting logic
- [ ] Filter application
- [ ] Export functionality

### Integration Tests:
- [ ] Search index building
- [ ] Database query performance
- [ ] Cross-repository search
- [ ] Search history persistence

### Performance Tests:
- [ ] Search response time benchmarks
- [ ] Large dataset search performance
- [ ] Memory usage with large indexes
- [ ] Concurrent search handling

## Time Breakdown

| Subtask | Estimated Hours | Priority |
|---------|----------------|----------|
| Fuse.js Integration | 10 | High |
| Search UI & Filters | 8 | High |
| Result Highlighting | 6 | High |
| Search History | 4 | Medium |
| Advanced Search | 6 | Medium |
| Result Export | 4 | Low |
| **Total** | **38 hours** | |

## Success Metrics

### Performance Targets:
- [ ] Search response < 500ms
- [ ] Index building < 30s per repository
- [ ] Memory usage < 100MB for search index
- [ ] UI responsiveness during search

### User Experience Goals:
- [ ] Relevant search results
- [ ] Intuitive search interface
- [ ] Fast result navigation
- [ ] Helpful search suggestions

## Potential Challenges and Mitigation

### Challenge 1: Search Index Size and Performance
**Risk**: Large repositories causing memory issues
**Mitigation**: Implement index chunking, lazy loading, efficient data structures

### Challenge 2: Search Relevance Tuning
**Risk**: Poor search result relevance
**Mitigation**: Extensive testing with real queries, configurable Fuse.js parameters

### Challenge 3: Cross-Repository Search Complexity
**Risk**: Complex logic for searching across multiple repositories
**Mitigation**: Clear architecture separation, comprehensive testing

### Challenge 4: Advanced Search Complexity
**Risk**: Complex query parsing becoming error-prone
**Mitigation**: Robust parser with extensive test coverage, clear error messages

## Definition of Done

- [ ] All subtasks completed and tested
- [ ] Search performance meets targets
- [ ] All search features functional
- [ ] Export functionality working
- [ ] Integration with reading view complete
- [ ] Search history operational
- [ ] Cross-platform compatibility verified
- [ ] User acceptance testing passed

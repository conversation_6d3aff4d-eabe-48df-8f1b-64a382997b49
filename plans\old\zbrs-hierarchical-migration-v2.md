# ZBRS Hierarchical Repository Implementation Plan v2.0

## Executive Summary

This document outlines the complete implementation of the proper ZBRS hierarchical design where repositories are collections containing multiple translations. This replaces the current flawed architecture where individual translations were incorrectly treated as repositories.

**Fresh Implementation**: This is a clean implementation with no backwards compatibility. The database starts fresh.

## Current Architecture Problems

### 1. Conceptual Misalignment
- **Current**: Individual translations (e.g., "KJV 1769") are treated as repositories
- **Correct**: Repositories are collections (e.g., "Official Zaphnath Bible Repository") containing multiple translations

### 2. Database Schema Issues
- `repository_id` in books/verses tables points to individual translations
- No proper parent-child relationship between repositories and translations
- Import process creates repository entries for individual translations

### 3. UI/UX Problems
- Repository discovery shows individual translations as importable units
- No translation selection during import process
- Repository management doesn't show hierarchical structure

### 4. Import Logic Issues
- Imports individual translation manifests directly
- No support for importing repositories with translation selection
- Missing validation for parent repository manifests

## Target Architecture (ZBRS v1.0 Compliant)

### Repository Hierarchy
```
Repository (Parent)
├── Translation 1 (Child)
│   ├── Books
│   └── Verses
├── Translation 2 (Child)
│   ├── Books
│   └── Verses
└── Translation N (Child)
    ├── Books
    └── Verses
```

### Import Flow
1. User discovers repositories (collections)
2. User selects a repository to import
3. System fetches repository manifest and shows available translations
4. User selects which translations to import (all selected by default)
5. System imports repository + selected translations

## Implementation Plan

### Phase 1: Database Schema Implementation

#### 1.1 ZBRS Hierarchical Database Schema
```sql
-- Repositories table supporting both parent repositories and translations
CREATE TABLE repositories (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT,
    type TEXT NOT NULL CHECK (type IN ('parent', 'translation')),
    parent_id TEXT,
    language_code TEXT, -- Only for translation type
    language_name TEXT, -- Only for translation type
    version TEXT NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    imported_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    metadata JSON NOT NULL DEFAULT '{}',
    FOREIGN KEY (parent_id) REFERENCES repositories(id) ON DELETE CASCADE
);

-- Repository translations relationship tracking
CREATE TABLE repository_translations (
    id TEXT PRIMARY KEY,
    parent_repository_id TEXT NOT NULL,
    translation_repository_id TEXT NOT NULL,
    directory_name TEXT NOT NULL,
    status TEXT DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'importing')),
    import_selected BOOLEAN DEFAULT TRUE,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (parent_repository_id) REFERENCES repositories(id) ON DELETE CASCADE,
    FOREIGN KEY (translation_repository_id) REFERENCES repositories(id) ON DELETE CASCADE,
    UNIQUE(parent_repository_id, translation_repository_id)
);

-- Performance indexes
CREATE INDEX idx_repositories_parent ON repositories(parent_id);
CREATE INDEX idx_repositories_type ON repositories(type);
CREATE INDEX idx_repository_translations_parent ON repository_translations(parent_repository_id);
CREATE INDEX idx_books_repository ON books(repository_id);
CREATE INDEX idx_verses_location ON verses(repository_id, book_id, chapter, verse);
```

This schema directly implements the ZBRS v1.0 hierarchical design as documented in the ZBRS Implementation Guide. The existing books and verses tables remain unchanged in structure - only their foreign key relationships now point to translation repositories instead of treating individual translations as repositories.

### Phase 2: Type System Updates

#### 2.1 Update Type Definitions
**File**: `types/env.d.ts`

```typescript
declare namespace Zaphnath {
  interface BibleRepository {
    id: string;
    name: string;
    description: string;
    type: 'parent' | 'translation';
    parent_id?: string; // Only for translation type
    language_code?: string; // Only for translation type
    language_name?: string; // Only for translation type
    version: string;
    created_at: string;
    updated_at: string;
    imported_at: string;
    metadata: Record<string, any>;
    
    // Computed fields for UI
    translations?: BibleRepository[]; // Only for parent type
    translation_count?: number; // Only for parent type
  }

  interface RepositoryTranslation {
    id: string;
    parent_repository_id: string;
    translation_repository_id: string;
    directory_name: string;
    status: 'active' | 'inactive' | 'importing';
    import_selected: boolean;
    created_at: string;
  }

  // Updated API interfaces
  interface RepositoryAPI {
    // Repository management
    getRepositories(): Promise<BibleRepository[]>;
    getParentRepositories(): Promise<BibleRepository[]>;
    getTranslations(parentId: string): Promise<BibleRepository[]>;
    
    // Discovery and import
    discover(): Promise<RepositoryIndexEntry[]>;
    fetchRepositoryIndex(url: string): Promise<RepositoryIndexEntry[]>;
    getRepositoryManifest(url: string): Promise<ZBRSManifest>;
    
    // New hierarchical import
    importRepository(url: string, selectedTranslations: string[]): Promise<ImportResult>;
    validateRepository(url: string): Promise<ValidationResult>;
    
    // Legacy methods (deprecated)
    import(url: string, options?: any): Promise<ImportResult>;
  }

  interface ImportResult {
    success: boolean;
    repository_id: string;
    translations_imported: string[];
    translations_skipped: string[];
    books_imported: number;
    errors: string[];
    warnings: string[];
    duration_ms: number;
  }
}
```

#### 2.2 ZBRS Manifest Types
**File**: `packages/main/src/services/repository/types.ts`

```typescript
// Parent Repository Manifest
export interface ZBRSParentManifest {
  zbrs_version: string;
  repository: {
    id: string;
    name: string;
    description: string;
    version: string;
    type: 'parent';
    created_at: string;
    updated_at: string;
  };
  publisher: {
    name: string;
    url?: string;
    contact?: string;
  };
  translations: TranslationReference[];
  technical: {
    encoding: 'UTF-8';
    compression?: 'none' | 'gzip' | 'brotli';
    checksum?: string;
    size_bytes?: number;
  };
  extensions?: Record<string, any>;
}

export interface TranslationReference {
  id: string;
  name: string;
  directory: string;
  language: {
    code: string;
    name: string;
    direction: 'ltr' | 'rtl';
  };
  status: 'active' | 'inactive' | 'deprecated';
  version?: string;
  description?: string;
  last_updated?: string;
}

// Translation Manifest (existing ZBRSManifest with type field)
export interface ZBRSTranslationManifest extends Omit<ZBRSManifest, 'repository'> {
  repository: {
    id: string;
    name: string;
    description: string;
    version: string;
    type?: 'translation'; // Optional for backwards compatibility
    language: {
      code: string;
      name: string;
      direction: 'ltr' | 'rtl';
      script?: string;
    };
    translation: {
      type: 'formal' | 'dynamic' | 'paraphrase' | 'interlinear';
      year: number;
      copyright: string;
      license: string;
      source: string;
      translators?: string[];
    };
    publisher: {
      name: string;
      url?: string;
      contact?: string;
    };
    created_at: string;
    updated_at: string;
  };
}
```

### Phase 3: Service Layer Updates

#### 3.1 Repository Service Refactoring
**File**: `packages/main/src/services/repository/index.ts`

Key changes needed:
- Add `importRepository()` method for hierarchical imports
- Update `getRepositories()` to return parent repositories with translation counts
- Add `getTranslations(parentId)` method
- Modify validation to handle both parent and translation manifests

#### 3.2 Database Service Updates
**File**: `packages/main/src/services/database/index.ts`

Key changes needed:
- Update `getRepositories()` to handle hierarchical queries
- Add methods for parent-translation relationships
- Update book/verse queries to work with new schema

### Phase 4: UI Component Updates

#### 4.1 Repository Discovery Component
**File**: `packages/renderer/src/components/repository/RepositoryDiscovery.tsx`

Changes needed:
- Show actual repositories (collections) instead of individual translations
- When user clicks a repository, show available translations with checkboxes
- Allow user to select/deselect translations before import
- Update button text from "View" to "Import" for final repositories

#### 4.2 Repository Management Component
**File**: `packages/renderer/src/components/repository/RepositoryManagement.tsx`

Changes needed:
- Display parent repositories with expandable translation lists
- Show translation count for each parent repository
- Allow individual translation management (enable/disable)

#### 4.3 New Translation Selection Component
**File**: `packages/renderer/src/components/repository/TranslationSelector.tsx`

New component needed:
- Display available translations from a repository manifest
- Checkboxes for each translation (all selected by default)
- Language and description information for each translation
- Import button that passes selected translations to import service

### Phase 5: Import Flow Redesign

#### 5.1 New Import Process
1. **Repository Discovery**: User sees actual repositories (collections)
2. **Repository Selection**: User clicks "Import" on a repository
3. **Translation Selection**: System fetches repository manifest and shows available translations
4. **Import Execution**: System imports repository + selected translations
5. **Post-Import**: User can manage translations within the imported repository

#### 5.2 Import Service Updates
- Validate repository manifest (parent type)
- Fetch and validate each selected translation manifest
- Create parent repository entry
- Create translation repository entries
- Import content for each selected translation
- Create relationship entries in `repository_translations`

## Implementation Order

### Week 1: Foundation
1. Create and test database migration scripts
2. Update type definitions
3. Update database service methods

### Week 2: Service Layer
1. Refactor repository service for hierarchical support
2. Update import logic for new flow
3. Add validation for parent manifests

### Week 3: UI Components
1. Update repository discovery component
2. Create translation selection component
3. Update repository management component

### Week 4: Integration & Testing
1. Connect all components with new import flow
2. Test migration with existing data
3. Update documentation and error handling

## Risk Mitigation

### Data Loss Prevention
- Create complete database backup before migration
- Test migration on copy of production data
- Implement rollback procedures

### User Experience
- Provide clear migration notifications
- Update help documentation
- Consider migration assistant UI

### Performance
- Optimize queries for hierarchical structure
- Add appropriate database indexes
- Test with large datasets

## Success Criteria

1. **Functional**: Users can import repositories and select specific translations
2. **Data Integrity**: All existing translation data is preserved and properly migrated
3. **Performance**: Repository operations perform as well as or better than current implementation
4. **User Experience**: Import flow is intuitive and matches ZBRS design principles

## Post-Migration Cleanup

1. Remove deprecated import methods
2. Update API documentation
3. Remove old type definitions
4. Clean up unused database columns
5. Update user documentation and help system

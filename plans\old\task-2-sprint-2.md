# Task 2: Verse Selection and Highlighting

**Sprint**: 2  
**Priority**: High ⭐  
**Estimated Time**: 2 days  
**Status**: NOT STARTED

## Overview

Implement comprehensive verse selection and highlighting functionality that allows users to select individual verses or ranges, apply multiple highlight colors, and perform actions on selected text. This task creates the foundation for user interaction with Bible text.

## Technical Implementation Details

### Component Architecture
```
VerseSelection/
├── SelectionManager.tsx         # Main selection logic
├── HighlightRenderer.tsx        # Highlight display component
├── SelectionContextMenu.tsx     # Right-click context menu
├── HighlightPalette.tsx        # Color selection interface
├── SelectionToolbar.tsx        # Selection action toolbar
└── hooks/
    ├── useTextSelection.ts      # Text selection hook
    ├── useHighlights.ts         # Highlight management
    └── useSelectionActions.ts   # Selection actions
```

### State Management Integration
```typescript
interface SelectionState {
  currentSelection: VerseSelection | null;
  highlights: HighlightData[];
  selectionMode: 'single' | 'range' | 'multi';
  activeHighlightColor: string;
}

interface VerseSelection {
  startVerse: VerseReference;
  endVerse: VerseReference;
  selectedText: string;
  selectionRange: Range;
}

interface HighlightData {
  id: string;
  translationId: string;
  verseReferences: VerseReference[];
  color: string;
  note?: string;
  createdAt: Date;
  updatedAt: Date;
}
```

### Database Schema Requirements
```sql
-- Highlights table
CREATE TABLE highlights (
  id TEXT PRIMARY KEY,
  translation_id TEXT NOT NULL, -- Foreign key to the translation's ID in the repositories table
  book_id INTEGER NOT NULL,
  start_chapter INTEGER NOT NULL,
  start_verse INTEGER NOT NULL,
  end_chapter INTEGER NOT NULL,
  end_verse INTEGER NOT NULL,
  color TEXT NOT NULL,
  note TEXT,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (translation_id) REFERENCES repositories(id)
);

-- Selection history for quick access
CREATE TABLE selection_history (
  id INTEGER PRIMARY KEY,
  verse_reference TEXT NOT NULL,
  selected_text TEXT NOT NULL,
  action_taken TEXT, -- 'copy', 'highlight', 'bookmark', 'note'
  timestamp DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

## Detailed Subtask Breakdown

### Subtask 2.1: Implement Verse Selection Logic (6 hours)
**Acceptance Criteria:**
- [ ] Single verse selection with click
- [ ] Range selection with shift+click
- [ ] Multi-verse selection with ctrl+click
- [ ] Selection respects verse boundaries
- [ ] Visual feedback for selected verses

**Implementation Approach:**
```typescript
const useTextSelection = () => {
  const [selection, setSelection] = useState<VerseSelection | null>(null);
  
  const handleVerseClick = useCallback((verseRef: VerseReference, event: MouseEvent) => {
    if (event.shiftKey) {
      // Range selection
      handleRangeSelection(verseRef);
    } else if (event.ctrlKey || event.metaKey) {
      // Multi selection
      handleMultiSelection(verseRef);
    } else {
      // Single selection
      handleSingleSelection(verseRef);
    }
  }, []);
  
  return { selection, handleVerseClick };
};
```

**Technical Challenges:**
- Maintaining selection across virtual scrolling
- Handling selection in different verse layouts
- Cross-browser selection API differences

### Subtask 2.2: Add Highlighting with Multiple Colors (8 hours)
**Acceptance Criteria:**
- [ ] 8+ predefined highlight colors
- [ ] Custom color picker option
- [ ] Highlight persistence across sessions
- [ ] Highlight rendering performance
- [ ] Highlight overlap handling

**Color Palette:**
```typescript
const HIGHLIGHT_COLORS = {
  yellow: '#FEF3C7',
  green: '#D1FAE5',
  blue: '#DBEAFE',
  purple: '#E9D5FF',
  pink: '#FCE7F3',
  orange: '#FED7AA',
  red: '#FEE2E2',
  gray: '#F3F4F6'
};
```

**Highlight Rendering Strategy:**
- Use CSS `background-color` with transparency
- Handle overlapping highlights with gradient blending
- Optimize rendering for large chapters

### Subtask 2.3: Create Selection Context Menu (4 hours)
**Acceptance Criteria:**
- [ ] Right-click shows context menu
- [ ] Context-sensitive menu options
- [ ] Keyboard shortcuts for menu actions
- [ ] Menu positioning handles screen edges
- [ ] Touch device support

**Context Menu Actions:**
```typescript
interface ContextMenuAction {
  label: string;
  icon: string;
  shortcut?: string;
  action: (selection: VerseSelection) => void;
  condition?: (selection: VerseSelection) => boolean;
}

const CONTEXT_ACTIONS: ContextMenuAction[] = [
  {
    label: 'Copy Verse',
    icon: 'copy',
    shortcut: 'Ctrl+C',
    action: copySelection
  },
  {
    label: 'Highlight',
    icon: 'highlight',
    shortcut: 'Ctrl+H',
    action: showHighlightPalette
  },
  {
    label: 'Add Note',
    icon: 'note',
    shortcut: 'Ctrl+N',
    action: createNote
  },
  {
    label: 'Bookmark',
    icon: 'bookmark',
    shortcut: 'Ctrl+B',
    action: addBookmark
  }
];
```

### Subtask 2.4: Build Verse Reference Display and Copying (3 hours)
**Acceptance Criteria:**
- [ ] Automatic verse reference generation
- [ ] Multiple citation formats (MLA, APA, Chicago)
- [ ] Copy with attribution
- [ ] Customizable reference format
- [ ] Translation name inclusion

**Reference Formatting:**
```typescript
interface CitationFormat {
  name: string;
  template: string;
  example: string;
}

const CITATION_FORMATS: CitationFormat[] = [
  {
    name: 'Standard',
    template: '{book} {chapter}:{verse} ({translation})',
    example: 'John 3:16 (ESV)'
  },
  {
    name: 'MLA',
    template: '{book} {chapter}.{verse}. {translation}',
    example: 'John 3.16. ESV'
  },
  {
    name: 'APA',
    template: '{book} {chapter}:{verse} ({translation})',
    example: 'John 3:16 (English Standard Version)'
  }
];
```

### Subtask 2.5: Add Selection Persistence (4 hours)
**Acceptance Criteria:**
- [ ] Highlights saved to database
- [ ] Highlights restored on app restart
- [ ] Highlights are correctly scoped to their specific translation
- [ ] Highlight backup and restore
- [ ] Performance with large highlight datasets

**Persistence Strategy:**
```typescript
const useHighlightPersistence = () => {
  const saveHighlight = useCallback(async (highlight: HighlightData) => {
    await window.electronAPI.database.execute(
      'INSERT INTO highlights (id, translation_id, book_id, start_chapter, start_verse, end_chapter, end_verse, color, note) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)',
      [highlight.id, highlight.translationId, highlight.bookId, highlight.startChapter, highlight.startVerse, highlight.endChapter, highlight.endVerse, highlight.color, highlight.note]
    );
  }, []);
  
  const loadHighlights = useCallback(async (translationId: string, bookId: number) => {
    return await window.electronAPI.database.query(
      'SELECT * FROM highlights WHERE translation_id = ? AND book_id = ?',
      [translationId, bookId]
    );
  }, []);
  
  return { saveHighlight, loadHighlights };
};
```

### Subtask 2.6: Implement Selection Keyboard Shortcuts (3 hours)
**Acceptance Criteria:**
- [ ] Keyboard shortcuts work consistently
- [ ] Shortcuts don't conflict with browser defaults
- [ ] Visual feedback for keyboard actions
- [ ] Customizable shortcut keys
- [ ] Help documentation for shortcuts

**Keyboard Shortcuts:**
- `Ctrl+A`: Select all verses in chapter
- `Ctrl+C`: Copy selected verses
- `Ctrl+H`: Highlight selected verses
- `Ctrl+N`: Add note to selection
- `Ctrl+B`: Bookmark selection
- `Escape`: Clear selection

## Dependencies and Integration Points

### Sprint 1 Integration:
- **Database Service**: Store and retrieve highlights
- **Settings Provider**: Highlight preferences and citation formats
- **Theme System**: Highlight colors that work with themes
- **UI Store**: Selection state management

### Task 1 Integration:
- **Reading View**: Integrate selection with verse rendering
- **Navigation**: Maintain selection across navigation
- **Keyboard Handler**: Coordinate keyboard shortcuts

### External Dependencies:
```json
{
  "uuid": "^9.0.0",
  "color": "^4.2.3",
  "react-hotkeys-hook": "^4.4.1"
}
```

## Testing Considerations

### Unit Tests:
- [ ] Selection logic with different verse ranges
- [ ] Highlight color application and removal
- [ ] Citation format generation
- [ ] Keyboard shortcut handling

### Integration Tests:
- [ ] Database persistence of highlights
- [ ] Highlights are correctly scoped per translation
- [ ] Theme integration with highlight colors
- [ ] Performance with large highlight datasets

### User Experience Tests:
- [ ] Selection accuracy on different screen sizes
- [ ] Context menu positioning
- [ ] Keyboard navigation accessibility
- [ ] Touch device interaction

## Time Breakdown

| Subtask | Estimated Hours | Priority |
|---------|----------------|----------|
| Verse Selection Logic | 6 | High |
| Highlighting System | 8 | High |
| Context Menu | 4 | High |
| Reference Display | 3 | Medium |
| Selection Persistence | 4 | High |
| Keyboard Shortcuts | 3 | Medium |
| **Total** | **28 hours** | |

## Success Metrics

### Performance Targets:
- [ ] Selection response < 50ms
- [ ] Highlight rendering < 100ms
- [ ] Context menu display < 30ms
- [ ] Database operations < 200ms

### User Experience Goals:
- [ ] Intuitive selection behavior
- [ ] Consistent highlight appearance
- [ ] Reliable persistence
- [ ] Accessible keyboard navigation

## Potential Challenges and Mitigation

### Challenge 1: Browser Selection API Inconsistencies
**Risk**: Different behavior across browsers/platforms
**Mitigation**: Extensive cross-browser testing, fallback implementations

### Challenge 2: Performance with Many Highlights
**Risk**: Slow rendering with hundreds of highlights
**Mitigation**: Virtualization-aware highlighting, efficient data structures

### Challenge 3: Highlight Overlap Complexity
**Risk**: Complex visual rendering with overlapping highlights
**Mitigation**: Simple overlap strategy, clear visual hierarchy

### Challenge 4: Touch Device Selection
**Risk**: Poor selection experience on tablets/touch screens
**Mitigation**: Touch-optimized selection gestures, alternative selection methods

## Definition of Done

- [ ] All subtasks completed and tested
- [ ] Selection works across all verse types
- [ ] Highlights persist correctly
- [ ] Context menu functions properly
- [ ] Keyboard shortcuts operational
- [ ] Performance targets met
- [ ] Cross-platform compatibility verified
- [ ] Integration with Task 1 complete

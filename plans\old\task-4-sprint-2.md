# Task 4: Reading Customization Controls

**Sprint**: 2  
**Priority**: Medium  
**Estimated Time**: 2 days  
**Status**: NOT STARTED

## Overview

Create comprehensive reading customization controls that allow users to personalize their Bible reading experience. This includes font controls, spacing options, display preferences, and preset reading modes that enhance readability and accessibility.

## Technical Implementation Details

### Component Architecture
```
ReadingCustomization/
├── CustomizationPanel.tsx       # Main customization interface
├── FontControls.tsx            # Font family and size controls
├── SpacingControls.tsx         # Line and paragraph spacing
├── DisplayOptions.tsx          # Verse numbers, formatting options
├── ReadingModePresets.tsx      # Preset configurations
├── AccessibilityControls.tsx   # Accessibility features
└── hooks/
    ├── useReadingPreferences.ts # Preference management
    ├── usePresets.ts           # Preset management
    └── useFontLoader.ts        # Dynamic font loading
```

### State Management Integration
```typescript
interface ReadingPreferences {
  font: {
    family: string;
    size: number;
    weight: 'normal' | 'bold';
    lineHeight: number;
  };
  spacing: {
    paragraphSpacing: number;
    verseSpacing: number;
    marginWidth: number;
    contentWidth: 'narrow' | 'medium' | 'wide' | 'full';
  };
  display: {
    showVerseNumbers: boolean;
    verseNumberStyle: 'inline' | 'margin' | 'superscript';
    showChapterNumbers: boolean;
    highlightCurrentVerse: boolean;
    showParagraphMarks: boolean;
  };
  accessibility: {
    highContrast: boolean;
    reducedMotion: boolean;
    focusIndicators: boolean;
    fontSize: 'small' | 'medium' | 'large' | 'extra-large';
  };
  activePreset?: string;
}
```

### Database Schema Requirements
```sql
-- Reading preferences
CREATE TABLE reading_preferences (
  id INTEGER PRIMARY KEY,
  user_id TEXT DEFAULT 'default',
  preferences TEXT NOT NULL, -- JSON string
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- Reading mode presets
CREATE TABLE reading_presets (
  id TEXT PRIMARY KEY,
  name TEXT NOT NULL,
  description TEXT,
  preferences TEXT NOT NULL, -- JSON string
  is_built_in BOOLEAN DEFAULT FALSE,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- Insert built-in presets
INSERT INTO reading_presets (id, name, description, preferences, is_built_in) VALUES
('study', 'Study Mode', 'Optimized for detailed Bible study', '{"font":{"family":"Georgia","size":16},"spacing":{"contentWidth":"medium"}}', TRUE),
('reading', 'Reading Mode', 'Comfortable for extended reading', '{"font":{"family":"Charter","size":18},"spacing":{"contentWidth":"narrow"}}', TRUE),
('presentation', 'Presentation Mode', 'Large text for presentations', '{"font":{"family":"Arial","size":24},"spacing":{"contentWidth":"wide"}}', TRUE);
```

## Detailed Subtask Breakdown

### Subtask 4.1: Create Font Family and Size Controls (6 hours)
**Acceptance Criteria:**
- [ ] Font family selection with preview
- [ ] Font size slider with live preview
- [ ] Font weight options (normal, bold)
- [ ] Line height adjustment
- [ ] Web-safe and Google Fonts integration

**Font Management:**
```typescript
const AVAILABLE_FONTS = [
  // Web-safe fonts
  { name: 'Georgia', family: 'Georgia, serif', category: 'serif' },
  { name: 'Times New Roman', family: '"Times New Roman", serif', category: 'serif' },
  { name: 'Arial', family: 'Arial, sans-serif', category: 'sans-serif' },
  { name: 'Helvetica', family: 'Helvetica, sans-serif', category: 'sans-serif' },
  
  // Reading-optimized fonts
  { name: 'Charter', family: 'Charter, Georgia, serif', category: 'serif' },
  { name: 'Source Serif Pro', family: '"Source Serif Pro", serif', category: 'serif' },
  { name: 'Source Sans Pro', family: '"Source Sans Pro", sans-serif', category: 'sans-serif' },
  { name: 'Open Sans', family: '"Open Sans", sans-serif', category: 'sans-serif' }
];

const FontControls: React.FC = () => {
  const { preferences, updatePreferences } = useReadingPreferences();
  
  const handleFontChange = (fontFamily: string) => {
    updatePreferences({
      font: {
        ...preferences.font,
        family: fontFamily
      }
    });
  };
  
  const handleSizeChange = (size: number) => {
    updatePreferences({
      font: {
        ...preferences.font,
        size: Math.max(12, Math.min(32, size))
      }
    });
  };
  
  return (
    <div className="font-controls">
      <FontFamilySelector 
        fonts={AVAILABLE_FONTS}
        selected={preferences.font.family}
        onChange={handleFontChange}
      />
      <FontSizeSlider
        value={preferences.font.size}
        min={12}
        max={32}
        onChange={handleSizeChange}
      />
    </div>
  );
};
```

### Subtask 4.2: Implement Line and Paragraph Spacing (4 hours)
**Acceptance Criteria:**
- [ ] Line height adjustment (1.0 - 2.5)
- [ ] Paragraph spacing controls
- [ ] Verse spacing options
- [ ] Content width presets
- [ ] Margin controls

**Spacing Controls Implementation:**
```typescript
const SpacingControls: React.FC = () => {
  const { preferences, updatePreferences } = useReadingPreferences();
  
  const spacingOptions = {
    lineHeight: { min: 1.0, max: 2.5, step: 0.1 },
    paragraphSpacing: { min: 0, max: 2, step: 0.25 },
    verseSpacing: { min: 0, max: 1, step: 0.1 }
  };
  
  const contentWidthOptions = [
    { value: 'narrow', label: 'Narrow (600px)', description: 'Best for reading' },
    { value: 'medium', label: 'Medium (800px)', description: 'Balanced layout' },
    { value: 'wide', label: 'Wide (1000px)', description: 'More content visible' },
    { value: 'full', label: 'Full Width', description: 'Use all available space' }
  ];
  
  return (
    <div className="spacing-controls">
      <SliderControl
        label="Line Height"
        value={preferences.font.lineHeight}
        {...spacingOptions.lineHeight}
        onChange={(value) => updatePreferences({
          font: { ...preferences.font, lineHeight: value }
        })}
      />
      
      <SliderControl
        label="Paragraph Spacing"
        value={preferences.spacing.paragraphSpacing}
        {...spacingOptions.paragraphSpacing}
        onChange={(value) => updatePreferences({
          spacing: { ...preferences.spacing, paragraphSpacing: value }
        })}
      />
      
      <RadioGroup
        label="Content Width"
        options={contentWidthOptions}
        value={preferences.spacing.contentWidth}
        onChange={(value) => updatePreferences({
          spacing: { ...preferences.spacing, contentWidth: value }
        })}
      />
    </div>
  );
};
```

### Subtask 4.3: Add Reading Width and Margin Controls (3 hours)
**Acceptance Criteria:**
- [ ] Content width adjustment
- [ ] Left and right margin controls
- [ ] Responsive behavior on different screen sizes
- [ ] Preview of layout changes
- [ ] Optimal reading width suggestions

**Layout Control Logic:**
```typescript
const useLayoutCalculation = () => {
  const calculateOptimalWidth = (fontSize: number, screenWidth: number): number => {
    // Optimal reading width: 45-75 characters per line
    const averageCharWidth = fontSize * 0.6; // Approximate character width
    const optimalChars = 65; // Target characters per line
    const calculatedWidth = optimalChars * averageCharWidth;
    
    // Don't exceed 80% of screen width
    return Math.min(calculatedWidth, screenWidth * 0.8);
  };
  
  const getContentStyles = (preferences: ReadingPreferences, screenWidth: number) => {
    let width: string;
    
    switch (preferences.spacing.contentWidth) {
      case 'narrow':
        width = '600px';
        break;
      case 'medium':
        width = '800px';
        break;
      case 'wide':
        width = '1000px';
        break;
      case 'full':
        width = '100%';
        break;
      default:
        width = `${calculateOptimalWidth(preferences.font.size, screenWidth)}px`;
    }
    
    return {
      maxWidth: width,
      margin: '0 auto',
      padding: `0 ${preferences.spacing.marginWidth}px`,
      fontSize: `${preferences.font.size}px`,
      fontFamily: preferences.font.family,
      lineHeight: preferences.font.lineHeight,
      fontWeight: preferences.font.weight
    };
  };
  
  return { calculateOptimalWidth, getContentStyles };
};
```

### Subtask 4.4: Build Verse Display Options (4 hours)
**Acceptance Criteria:**
- [ ] Toggle verse numbers on/off
- [ ] Verse number styling options (inline, margin, superscript)
- [ ] Chapter number display options
- [ ] Current verse highlighting
- [ ] Paragraph mark visibility

**Display Options Component:**
```typescript
const DisplayOptions: React.FC = () => {
  const { preferences, updatePreferences } = useReadingPreferences();
  
  const verseNumberStyles = [
    { value: 'inline', label: 'Inline', description: 'Numbers within the text' },
    { value: 'margin', label: 'Margin', description: 'Numbers in left margin' },
    { value: 'superscript', label: 'Superscript', description: 'Small numbers above text' }
  ];
  
  return (
    <div className="display-options">
      <ToggleControl
        label="Show Verse Numbers"
        checked={preferences.display.showVerseNumbers}
        onChange={(checked) => updatePreferences({
          display: { ...preferences.display, showVerseNumbers: checked }
        })}
      />
      
      {preferences.display.showVerseNumbers && (
        <RadioGroup
          label="Verse Number Style"
          options={verseNumberStyles}
          value={preferences.display.verseNumberStyle}
          onChange={(value) => updatePreferences({
            display: { ...preferences.display, verseNumberStyle: value }
          })}
        />
      )}
      
      <ToggleControl
        label="Highlight Current Verse"
        checked={preferences.display.highlightCurrentVerse}
        onChange={(checked) => updatePreferences({
          display: { ...preferences.display, highlightCurrentVerse: checked }
        })}
      />
      
      <ToggleControl
        label="Show Paragraph Marks"
        checked={preferences.display.showParagraphMarks}
        onChange={(checked) => updatePreferences({
          display: { ...preferences.display, showParagraphMarks: checked }
        })}
      />
    </div>
  );
};
```

### Subtask 4.5: Create Reading Mode Presets (5 hours)
**Acceptance Criteria:**
- [ ] Built-in presets (Study, Reading, Presentation)
- [ ] Custom preset creation and management
- [ ] Preset preview functionality
- [ ] Import/export preset functionality
- [ ] Preset sharing capabilities

**Preset Management:**
```typescript
interface ReadingPreset {
  id: string;
  name: string;
  description: string;
  preferences: Partial<ReadingPreferences>;
  isBuiltIn: boolean;
  createdAt: Date;
}

const BUILT_IN_PRESETS: ReadingPreset[] = [
  {
    id: 'study',
    name: 'Study Mode',
    description: 'Optimized for detailed Bible study with clear verse numbers',
    preferences: {
      font: { family: 'Georgia', size: 16, lineHeight: 1.6 },
      spacing: { contentWidth: 'medium', paragraphSpacing: 0.5 },
      display: { showVerseNumbers: true, verseNumberStyle: 'margin' }
    },
    isBuiltIn: true,
    createdAt: new Date()
  },
  {
    id: 'reading',
    name: 'Reading Mode',
    description: 'Comfortable for extended reading sessions',
    preferences: {
      font: { family: 'Charter', size: 18, lineHeight: 1.8 },
      spacing: { contentWidth: 'narrow', paragraphSpacing: 1 },
      display: { showVerseNumbers: false, highlightCurrentVerse: false }
    },
    isBuiltIn: true,
    createdAt: new Date()
  },
  {
    id: 'presentation',
    name: 'Presentation Mode',
    description: 'Large text suitable for presentations and projection',
    preferences: {
      font: { family: 'Arial', size: 24, lineHeight: 1.4 },
      spacing: { contentWidth: 'wide', paragraphSpacing: 0.75 },
      display: { showVerseNumbers: true, verseNumberStyle: 'inline' }
    },
    isBuiltIn: true,
    createdAt: new Date()
  }
];

const usePresets = () => {
  const [presets, setPresets] = useState<ReadingPreset[]>([]);
  const { updatePreferences } = useReadingPreferences();
  
  const loadPresets = useCallback(async () => {
    const dbPresets = await window.electronAPI.database.query(
      'SELECT * FROM reading_presets ORDER BY is_built_in DESC, name ASC'
    );
    
    const allPresets = dbPresets.map(row => ({
      id: row.id,
      name: row.name,
      description: row.description,
      preferences: JSON.parse(row.preferences),
      isBuiltIn: row.is_built_in,
      createdAt: new Date(row.created_at)
    }));
    
    setPresets(allPresets);
  }, []);
  
  const applyPreset = useCallback((preset: ReadingPreset) => {
    updatePreferences(preset.preferences);
  }, [updatePreferences]);
  
  const saveCustomPreset = useCallback(async (name: string, description: string, preferences: ReadingPreferences) => {
    const id = `custom_${Date.now()}`;
    
    await window.electronAPI.database.execute(
      'INSERT INTO reading_presets (id, name, description, preferences) VALUES (?, ?, ?, ?)',
      [id, name, description, JSON.stringify(preferences)]
    );
    
    await loadPresets();
  }, [loadPresets]);
  
  return { presets, loadPresets, applyPreset, saveCustomPreset };
};
```

### Subtask 4.6: Add Zoom Controls for Accessibility (3 hours)
**Acceptance Criteria:**
- [ ] Zoom in/out functionality
- [ ] Keyboard shortcuts for zoom
- [ ] Zoom level indicator
- [ ] Reset to default zoom
- [ ] Accessibility compliance

**Zoom Implementation:**
```typescript
const useZoomControls = () => {
  const [zoomLevel, setZoomLevel] = useState(100);
  
  const zoomIn = useCallback(() => {
    setZoomLevel(prev => Math.min(200, prev + 10));
  }, []);
  
  const zoomOut = useCallback(() => {
    setZoomLevel(prev => Math.max(50, prev - 10));
  }, []);
  
  const resetZoom = useCallback(() => {
    setZoomLevel(100);
  }, []);
  
  // Apply zoom to document
  useEffect(() => {
    document.documentElement.style.fontSize = `${zoomLevel}%`;
    
    return () => {
      document.documentElement.style.fontSize = '';
    };
  }, [zoomLevel]);
  
  // Keyboard shortcuts
  useHotkeys('ctrl+plus', zoomIn);
  useHotkeys('ctrl+minus', zoomOut);
  useHotkeys('ctrl+0', resetZoom);
  
  return { zoomLevel, zoomIn, zoomOut, resetZoom };
};
```

## Dependencies and Integration Points

### Sprint 1 Integration:
- **Settings Provider**: Store reading preferences
- **Theme System**: Coordinate with theme colors
- **Database Service**: Persist preferences and presets

### Task 1 Integration:
- **Reading View**: Apply customization styles
- **Verse Component**: Implement display options

### External Dependencies:
```json
{
  "react-hotkeys-hook": "^4.4.1",
  "color": "^4.2.3"
}
```

## Testing Considerations

### Unit Tests:
- [ ] Preference state management
- [ ] Preset application logic
- [ ] Style calculation functions
- [ ] Accessibility features

### Integration Tests:
- [ ] Database persistence of preferences
- [ ] Theme integration
- [ ] Cross-component style application

### Accessibility Tests:
- [ ] Keyboard navigation
- [ ] Screen reader compatibility
- [ ] High contrast mode
- [ ] Focus indicators

## Time Breakdown

| Subtask | Estimated Hours | Priority |
|---------|----------------|----------|
| Font Controls | 6 | High |
| Spacing Controls | 4 | High |
| Width & Margins | 3 | Medium |
| Display Options | 4 | High |
| Reading Presets | 5 | Medium |
| Zoom Controls | 3 | Medium |
| **Total** | **25 hours** | |

## Success Metrics

### Performance Targets:
- [ ] Style changes apply < 100ms
- [ ] Preference persistence < 50ms
- [ ] UI responsiveness maintained
- [ ] Memory usage impact < 10MB

### User Experience Goals:
- [ ] Intuitive customization interface
- [ ] Immediate visual feedback
- [ ] Accessible to users with disabilities
- [ ] Preset system saves time

## Potential Challenges and Mitigation

### Challenge 1: Performance with Dynamic Styling
**Risk**: Style recalculation causing performance issues
**Mitigation**: Use CSS custom properties, debounce updates

### Challenge 2: Cross-Platform Font Availability
**Risk**: Fonts not available on all platforms
**Mitigation**: Robust font fallbacks, web font loading

### Challenge 3: Accessibility Compliance
**Risk**: Customization breaking accessibility features
**Mitigation**: Accessibility testing, WCAG compliance checks

## Definition of Done

- [ ] All subtasks completed and tested
- [ ] Customization applies immediately
- [ ] Preferences persist correctly
- [ ] Presets function properly
- [ ] Accessibility features working
- [ ] Performance targets met
- [ ] Cross-platform compatibility verified

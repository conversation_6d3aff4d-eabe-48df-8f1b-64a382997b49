{"zbrs_version": "1.0", "repository": {"id": "minimal-example", "name": "Minimal Example Bible Repository", "description": "A minimal example repository demonstrating the ZBRS v1.0 hierarchical standard", "version": "1.0.0", "type": "parent", "created_at": "2025-01-01T00:00:00Z", "updated_at": "2025-01-01T00:00:00Z"}, "publisher": {"name": "Beabfekad Zikie", "url": "https://github.com/beabzk/zaphnath", "contact": "<EMAIL>"}, "translations": [{"id": "kjv-1769-min", "name": "<PERSON> (1769) - <PERSON><PERSON>", "directory": "kjv-1769-min", "language": {"code": "en", "name": "English", "direction": "ltr"}, "status": "active"}], "technical": {"encoding": "UTF-8", "compression": "none", "checksum": "sha256:1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef", "size_bytes": 50000}}
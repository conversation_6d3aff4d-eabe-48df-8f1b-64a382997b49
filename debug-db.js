import Database from 'better-sqlite3';
import { join } from 'path';
import { homedir } from 'os';

// Mock app.getPath for Node.js environment
const mockApp = {
  getPath: (name) => {
    if (name === 'userData') {
      return join(homedir(), 'AppData', 'Roaming', 'zaphnath');
    }
    return homedir();
  }
};

const userDataPath = mockApp.getPath('userData');
const dbPath = join(userDataPath, 'databases', 'zaphnath.db');

console.log('Database path:', dbPath);

try {
  const db = new Database(dbPath);
  
  console.log('\n=== Migration Status ===');
  const migrations = db.prepare("SELECT * FROM migrations ORDER BY version").all();
  console.log('Applied migrations:');
  migrations.forEach(migration => {
    console.log(`  ${migration.version}: ${migration.name} (${migration.applied_at})`);
  });
  
  const currentVersion = db.prepare("SELECT MAX(version) as version FROM migrations").get();
  console.log(`\nCurrent version: ${currentVersion.version}`);
  
  console.log('\n=== Table Schema ===');
  const tables = db.prepare(`
    SELECT name FROM sqlite_master 
    WHERE type='table' AND name NOT LIKE 'sqlite_%'
    ORDER BY name
  `).all();
  
  for (const table of tables) {
    console.log(`\nTable: ${table.name}`);
    const schema = db.prepare(`PRAGMA table_info(${table.name})`).all();
    schema.forEach(column => {
      console.log(`  ${column.name}: ${column.type} ${column.notnull ? 'NOT NULL' : ''} ${column.dflt_value ? `DEFAULT ${column.dflt_value}` : ''}`);
    });
  }
  
  console.log('\n=== Repository Table Data ===');
  try {
    const repos = db.prepare("SELECT * FROM repositories LIMIT 5").all();
    console.log('Sample repositories:');
    repos.forEach(repo => {
      console.log(`  ${repo.id}: ${repo.name} (language: ${repo.language || 'NULL'})`);
    });
  } catch (error) {
    console.log('Error querying repositories:', error.message);
  }
  
  db.close();
} catch (error) {
  console.error('Error:', error);
}

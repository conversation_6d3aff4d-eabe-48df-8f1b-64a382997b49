# Task 5: Verse Comparison View

**Sprint**: 2  
**Priority**: Medium  
**Estimated Time**: 1.5 days  
**Status**: NOT STARTED

## Overview

Create a side-by-side verse comparison interface that allows users to compare the same passage across different translations. This feature enhances Bible study by enabling users to see how different translations render the same text, helping with deeper understanding and interpretation.

## Technical Implementation Details

### Component Architecture
```
VerseComparison/
├── ComparisonContainer.tsx      # Main comparison interface
├── ComparisonPanel.tsx         # Individual translation panel
├── TranslationSelector.tsx     # Translation selection dropdown
├── VerseAligner.tsx           # Verse alignment logic
├── ComparisonControls.tsx     # Comparison options and controls
├── ComparisonExport.tsx       # Export comparison functionality
└── hooks/
    ├── useComparison.ts       # Main comparison logic
    ├── useTranslationSync.ts  # Synchronization between translations
    └── useComparisonHistory.ts # Comparison history tracking
```

### State Management Integration
```typescript
interface ComparisonState {
  activeComparison: ComparisonData | null;
  selectedTranslations: string[];
  currentReference: VerseReference;
  comparisonMode: 'side-by-side' | 'stacked' | 'interleaved';
  syncScrolling: boolean;
  showDifferences: boolean;
}

interface ComparisonData {
  id: string;
  reference: VerseReference;
  translations: TranslationComparison[];
  createdAt: Date;
  notes?: string;
}

interface TranslationComparison {
  translationId: string;
  translationName: string;
  verses: VerseData[];
  metadata: {
    language: string;
    year: number;
    publisher: string;
  };
}

interface VerseData {
  chapter: number;
  verse: number;
  text: string;
  footnotes?: string[];
}
```

### Database Schema Requirements
```sql
-- Comparison history
CREATE TABLE comparison_history (
  id TEXT PRIMARY KEY,
  reference TEXT NOT NULL, -- JSON string of verse reference
  translations TEXT NOT NULL, -- JSON array of translation IDs
  comparison_mode TEXT DEFAULT 'side-by-side',
  notes TEXT,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- Comparison bookmarks (saved comparisons)
CREATE TABLE comparison_bookmarks (
  id TEXT PRIMARY KEY,
  name TEXT NOT NULL,
  description TEXT,
  reference TEXT NOT NULL,
  translations TEXT NOT NULL,
  settings TEXT, -- JSON string of comparison settings
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

## Detailed Subtask Breakdown

### Subtask 5.1: Create Side-by-Side Comparison Interface (8 hours)
**Acceptance Criteria:**
- [ ] Responsive side-by-side layout
- [ ] Support for 2-4 translations simultaneously
- [ ] Clean visual separation between translations
- [ ] Consistent verse alignment
- [ ] Smooth resizing and responsive behavior

**Layout Implementation:**
```typescript
const ComparisonContainer: React.FC = () => {
  const { 
    selectedTranslations, 
    currentReference, 
    comparisonMode,
    comparisonData 
  } = useComparison();
  
  const layoutConfig = useMemo(() => {
    const count = selectedTranslations.length;
    
    switch (comparisonMode) {
      case 'side-by-side':
        return {
          gridCols: Math.min(count, 4),
          maxWidth: '100%',
          panelMinWidth: '300px'
        };
      case 'stacked':
        return {
          gridCols: 1,
          maxWidth: '800px',
          panelMinWidth: '100%'
        };
      case 'interleaved':
        return {
          gridCols: 1,
          maxWidth: '900px',
          panelMinWidth: '100%'
        };
    }
  }, [selectedTranslations.length, comparisonMode]);
  
  return (
    <div className="comparison-container">
      <ComparisonControls />
      <div 
        className="comparison-grid"
        style={{
          display: 'grid',
          gridTemplateColumns: `repeat(${layoutConfig.gridCols}, 1fr)`,
          gap: '1rem',
          maxWidth: layoutConfig.maxWidth
        }}
      >
        {comparisonData?.translations.map((translation, index) => (
          <ComparisonPanel
            key={translation.translationId}
            translation={translation}
            reference={currentReference}
            index={index}
            minWidth={layoutConfig.panelMinWidth}
          />
        ))}
      </div>
    </div>
  );
};
```

### Subtask 5.2: Implement Translation Switching (4 hours)
**Acceptance Criteria:**
- [ ] Dropdown selection for each comparison panel
- [ ] Quick translation switching without losing position
- [ ] Available translation filtering
- [ ] Translation metadata display
- [ ] Recently used translations prioritization

**Translation Selection Logic:**
```typescript
const TranslationSelector: React.FC<{
  selectedTranslationId: string;
  onTranslationChange: (translationId: string) => void;
  position: number;
}> = ({ selectedTranslationId, onTranslationChange, position }) => {
  const { repositories } = useRepositoryStore();
  const { recentTranslations } = useComparisonHistory();
  
  const availableTranslations = useMemo(() => {
    return repositories
      .filter(repo => repo.type === 'bible')
      .map(repo => ({
        id: repo.id,
        name: repo.name,
        language: repo.metadata.language,
        year: repo.metadata.year,
        isRecent: recentTranslations.includes(repo.id)
      }))
      .sort((a, b) => {
        // Prioritize recent translations
        if (a.isRecent && !b.isRecent) return -1;
        if (!a.isRecent && b.isRecent) return 1;
        return a.name.localeCompare(b.name);
      });
  }, [repositories, recentTranslations]);
  
  return (
    <Select
      value={selectedTranslationId}
      onValueChange={onTranslationChange}
      placeholder={`Select translation ${position + 1}`}
    >
      <SelectTrigger className="translation-selector">
        <SelectValue />
      </SelectTrigger>
      <SelectContent>
        {availableTranslations.map(translation => (
          <SelectItem key={translation.id} value={translation.id}>
            <div className="translation-option">
              <span className="translation-name">{translation.name}</span>
              <span className="translation-meta">
                {translation.language} • {translation.year}
              </span>
              {translation.isRecent && (
                <Badge variant="secondary" size="sm">Recent</Badge>
              )}
            </div>
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  );
};
```

### Subtask 5.3: Add Verse Alignment and Synchronization (6 hours)
**Acceptance Criteria:**
- [ ] Verses aligned across translations
- [ ] Synchronized scrolling between panels
- [ ] Handling of verse numbering differences
- [ ] Current verse highlighting across all panels
- [ ] Smooth navigation synchronization

**Verse Alignment Implementation:**
```typescript
const useVerseAlignment = (translations: TranslationComparison[]) => {
  const [alignmentMap, setAlignmentMap] = useState<Map<string, VerseAlignment>>(new Map());
  
  interface VerseAlignment {
    standardReference: VerseReference;
    translationVerses: Map<string, VerseReference[]>;
    hasDiscrepancies: boolean;
  }
  
  const buildAlignmentMap = useCallback(() => {
    const map = new Map<string, VerseAlignment>();
    
    // Find the union of all verses across translations
    const allVerseRefs = new Set<string>();
    
    translations.forEach(translation => {
      translation.verses.forEach(verse => {
        const refKey = `${verse.chapter}:${verse.verse}`;
        allVerseRefs.add(refKey);
      });
    });
    
    // Create alignment for each verse
    allVerseRefs.forEach(refKey => {
      const [chapter, verse] = refKey.split(':').map(Number);
      const standardRef: VerseReference = { chapter, verse };
      
      const translationVerses = new Map<string, VerseReference[]>();
      let hasDiscrepancies = false;
      
      translations.forEach(translation => {
        const matchingVerses = translation.verses.filter(v => 
          v.chapter === chapter && v.verse === verse
        );
        
        if (matchingVerses.length === 0) {
          // Try to find nearby verses (verse numbering differences)
          const nearbyVerses = translation.verses.filter(v =>
            v.chapter === chapter && Math.abs(v.verse - verse) <= 1
          );
          
          if (nearbyVerses.length > 0) {
            hasDiscrepancies = true;
            translationVerses.set(translation.translationId, 
              nearbyVerses.map(v => ({ chapter: v.chapter, verse: v.verse }))
            );
          }
        } else {
          translationVerses.set(translation.translationId,
            matchingVerses.map(v => ({ chapter: v.chapter, verse: v.verse }))
          );
        }
      });
      
      map.set(refKey, {
        standardReference: standardRef,
        translationVerses,
        hasDiscrepancies
      });
    });
    
    setAlignmentMap(map);
  }, [translations]);
  
  useEffect(() => {
    buildAlignmentMap();
  }, [buildAlignmentMap]);
  
  return { alignmentMap, buildAlignmentMap };
};
```

### Subtask 5.4: Build Comparison Export Functionality (3 hours)
**Acceptance Criteria:**
- [ ] Export to multiple formats (PDF, Word, HTML)
- [ ] Include translation metadata
- [ ] Customizable export layout
- [ ] Verse reference formatting options
- [ ] Export with comparison notes

**Export Implementation:**
```typescript
interface ExportOptions {
  format: 'pdf' | 'docx' | 'html' | 'txt';
  layout: 'side-by-side' | 'stacked' | 'table';
  includeMetadata: boolean;
  includeNotes: boolean;
  verseReferenceFormat: string;
  pageSize?: 'letter' | 'a4';
  fontSize?: number;
}

const useComparisonExport = () => {
  const exportComparison = useCallback(async (
    comparison: ComparisonData,
    options: ExportOptions
  ) => {
    const exportData = {
      title: `Verse Comparison - ${formatReference(comparison.reference)}`,
      reference: comparison.reference,
      translations: comparison.translations,
      metadata: options.includeMetadata ? {
        exportDate: new Date(),
        exportOptions: options
      } : undefined,
      notes: options.includeNotes ? comparison.notes : undefined
    };
    
    switch (options.format) {
      case 'pdf':
        return await exportToPDF(exportData, options);
      case 'docx':
        return await exportToWord(exportData, options);
      case 'html':
        return await exportToHTML(exportData, options);
      case 'txt':
        return await exportToText(exportData, options);
    }
  }, []);
  
  const exportToPDF = async (data: any, options: ExportOptions) => {
    // Implementation using jsPDF or similar
    const doc = new jsPDF({
      orientation: options.layout === 'side-by-side' ? 'landscape' : 'portrait',
      unit: 'mm',
      format: options.pageSize || 'letter'
    });
    
    // Add content to PDF
    // ... PDF generation logic
    
    const fileName = `comparison-${formatReference(data.reference)}-${Date.now()}.pdf`;
    doc.save(fileName);
  };
  
  return { exportComparison };
};
```

### Subtask 5.5: Create Comparison History Tracking (2 hours)
**Acceptance Criteria:**
- [ ] Track comparison sessions
- [ ] Quick access to recent comparisons
- [ ] Comparison bookmarking
- [ ] Search comparison history
- [ ] History cleanup options

**History Management:**
```typescript
const useComparisonHistory = () => {
  const [history, setHistory] = useState<ComparisonHistoryItem[]>([]);
  const [bookmarks, setBookmarks] = useState<ComparisonBookmark[]>([]);
  
  const addToHistory = useCallback(async (comparison: ComparisonData) => {
    const historyItem: ComparisonHistoryItem = {
      id: comparison.id,
      reference: comparison.reference,
      translations: comparison.translations.map(t => t.translationId),
      timestamp: new Date()
    };
    
    await window.electronAPI.database.execute(
      'INSERT INTO comparison_history (id, reference, translations) VALUES (?, ?, ?)',
      [historyItem.id, JSON.stringify(historyItem.reference), JSON.stringify(historyItem.translations)]
    );
    
    setHistory(prev => [historyItem, ...prev.slice(0, 49)]); // Keep last 50
  }, []);
  
  const bookmarkComparison = useCallback(async (
    comparison: ComparisonData,
    name: string,
    description?: string
  ) => {
    const bookmark: ComparisonBookmark = {
      id: `bookmark_${Date.now()}`,
      name,
      description,
      reference: comparison.reference,
      translations: comparison.translations.map(t => t.repositoryId),
      settings: {
        comparisonMode: 'side-by-side',
        syncScrolling: true
      },
      createdAt: new Date()
    };
    
    await window.electronAPI.database.execute(
      'INSERT INTO comparison_bookmarks (id, name, description, reference, translations, settings) VALUES (?, ?, ?, ?, ?, ?)',
      [bookmark.id, bookmark.name, bookmark.description, JSON.stringify(bookmark.reference), JSON.stringify(bookmark.translations), JSON.stringify(bookmark.settings)]
    );
    
    setBookmarks(prev => [...prev, bookmark]);
  }, []);
  
  return { history, bookmarks, addToHistory, bookmarkComparison };
};
```

## Dependencies and Integration Points

### Sprint 1 Integration:
- **Repository Store**: Access translation repositories
- **Database Service**: Store comparison history
- **Settings Provider**: Comparison preferences

### Task 1 Integration:
- **Reading View**: Navigate from comparison to full reading
- **Navigation**: Integrate with verse navigation

### Task 2 Integration:
- **Verse Selection**: Compare selected verses
- **Highlighting**: Show highlights in comparison

### External Dependencies:
```json
{
  "jspdf": "^2.5.1",
  "html2canvas": "^1.4.1",
  "docx": "^8.5.0"
}
```

## Testing Considerations

### Unit Tests:
- [ ] Verse alignment logic
- [ ] Translation switching
- [ ] Export functionality
- [ ] History management

### Integration Tests:
- [ ] Multi-repository comparison
- [ ] Database persistence
- [ ] Export file generation

### User Experience Tests:
- [ ] Responsive layout behavior
- [ ] Scroll synchronization
- [ ] Translation switching performance

## Time Breakdown

| Subtask | Estimated Hours | Priority |
|---------|----------------|----------|
| Side-by-Side Interface | 8 | High |
| Translation Switching | 4 | High |
| Verse Alignment | 6 | High |
| Export Functionality | 3 | Medium |
| History Tracking | 2 | Low |
| **Total** | **23 hours** | |

## Success Metrics

### Performance Targets:
- [ ] Translation switching < 200ms
- [ ] Scroll synchronization < 50ms
- [ ] Export generation < 5s
- [ ] Layout rendering < 100ms

### User Experience Goals:
- [ ] Intuitive comparison interface
- [ ] Accurate verse alignment
- [ ] Smooth synchronization
- [ ] Useful export formats

## Potential Challenges and Mitigation

### Challenge 1: Verse Numbering Discrepancies
**Risk**: Different translations having different verse numbering
**Mitigation**: Flexible alignment algorithm, clear discrepancy indicators

### Challenge 2: Performance with Multiple Translations
**Risk**: Slow rendering with 3-4 translations
**Mitigation**: Virtualization, efficient rendering, lazy loading

### Challenge 3: Complex Export Formatting
**Risk**: Export layouts not matching screen display
**Mitigation**: Dedicated export templates, preview functionality

### Challenge 4: Responsive Design Complexity
**Risk**: Poor experience on smaller screens
**Mitigation**: Adaptive layout modes, mobile-optimized stacked view

## Definition of Done

- [ ] All subtasks completed and tested
- [ ] Side-by-side comparison functional
- [ ] Translation switching works smoothly
- [ ] Verse alignment handles discrepancies
- [ ] Export functionality operational
- [ ] History tracking working
- [ ] Responsive design verified
- [ ] Performance targets met

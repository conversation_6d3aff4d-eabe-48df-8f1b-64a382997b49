{"name": "@app/main", "type": "module", "author": {"email": "<EMAIL>", "name": "Beabfekad Zikie", "url": "https://beabzk.github.io"}, "scripts": {"build": "vite build", "typecheck": "tsc --noEmit"}, "exports": {".": {"types": "./src/index.ts", "default": "./dist/index.js"}}, "dependencies": {"@app/preload": "*", "@app/renderer": "*", "better-sqlite3": "^12.2.0", "electron-updater": "6.6.2"}, "devDependencies": {"@app/electron-versions": "*", "electron-devtools-installer": "^4.0.0", "electron-rebuild": "^3.2.9", "typescript": "^5.9.2", "vite": "^7.0.6"}}